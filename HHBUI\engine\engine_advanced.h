/**
 * @file engine_advanced.h
 * @brief HHBUI 引擎高级功能扩展头文件
 * @version 1.0.0.01250720
 * @date 2025-01-31
 * <AUTHOR> Team
 * 
 * 本文件定义了 HHBUI 引擎的高级功能，包括：
 * - 资源管理器
 * - 内存池管理
 * - 异步任务调度器
 * - 性能分析器
 * - 插件系统接口
 * 
 * 这些功能是可选的，可以根据需要启用或禁用
 */

#pragma once

#include "engine.h"
#include <future>
#include <queue>
#include <condition_variable>

namespace HHBUI {

// ==================== 资源管理器 ====================

/**
 * @brief 资源类型枚举
 */
enum class ResourceType : uint32_t {
    Unknown = 0,
    Image = 1,
    Font = 2,
    Shader = 3,
    Texture = 4,
    Audio = 5,
    Video = 6,
    Data = 7
};

/**
 * @brief 资源信息结构
 */
struct ResourceInfo {
    std::wstring name;                    ///< 资源名称
    std::wstring path;                    ///< 资源路径
    ResourceType type = ResourceType::Unknown; ///< 资源类型
    size_t size = 0;                      ///< 资源大小（字节）
    uint64_t lastModified = 0;            ///< 最后修改时间
    uint32_t refCount = 0;                ///< 引用计数
    bool isLoaded = false;                ///< 是否已加载
    std::shared_ptr<void> data = nullptr; ///< 资源数据
    
    /**
     * @brief 检查资源是否有效
     */
    [[nodiscard]] bool IsValid() const noexcept {
        return !name.empty() && type != ResourceType::Unknown;
    }
};

/**
 * @brief 高级资源管理器
 */
class TOAPI ResourceManager {
public:
    static ResourceManager& Instance();
    
    // 禁用拷贝和移动
    ResourceManager(const ResourceManager&) = delete;
    ResourceManager& operator=(const ResourceManager&) = delete;
    ResourceManager(ResourceManager&&) = delete;
    ResourceManager& operator=(ResourceManager&&) = delete;
    
    /**
     * @brief 加载资源
     * @param name 资源名称
     * @param path 资源路径
     * @param type 资源类型
     * @return 是否加载成功
     */
    [[nodiscard]] bool LoadResource(const std::wstring& name, 
                                   const std::wstring& path, 
                                   ResourceType type) noexcept;
    
    /**
     * @brief 异步加载资源
     * @param name 资源名称
     * @param path 资源路径
     * @param type 资源类型
     * @param callback 完成回调
     * @return 是否开始加载
     */
    [[nodiscard]] bool LoadResourceAsync(const std::wstring& name,
                                        const std::wstring& path,
                                        ResourceType type,
                                        std::function<void(bool)> callback) noexcept;
    
    /**
     * @brief 获取资源
     * @param name 资源名称
     * @return 资源信息，如果不存在返回nullptr
     */
    [[nodiscard]] std::shared_ptr<ResourceInfo> GetResource(const std::wstring& name) noexcept;
    
    /**
     * @brief 卸载资源
     * @param name 资源名称
     * @return 是否卸载成功
     */
    [[nodiscard]] bool UnloadResource(const std::wstring& name) noexcept;
    
    /**
     * @brief 清理未使用的资源
     * @return 清理的资源数量
     */
    [[nodiscard]] size_t CleanupUnusedResources() noexcept;
    
    /**
     * @brief 获取资源统计信息
     * @param totalCount 总资源数量
     * @param loadedCount 已加载资源数量
     * @param totalSize 总资源大小
     * @return 是否获取成功
     */
    [[nodiscard]] bool GetResourceStats(size_t& totalCount, size_t& loadedCount, size_t& totalSize) noexcept;
    
private:
    ResourceManager() = default;
    ~ResourceManager() = default;
    
    std::mutex m_mutex;
    std::unordered_map<std::wstring, std::shared_ptr<ResourceInfo>> m_resources;
};

// ==================== 内存池管理器 ====================

/**
 * @brief 内存池配置
 */
struct MemoryPoolConfig {
    size_t blockSize = 1024;              ///< 内存块大小
    size_t initialBlocks = 100;           ///< 初始内存块数量
    size_t maxBlocks = 10000;             ///< 最大内存块数量
    bool allowGrowth = true;              ///< 是否允许增长
    bool enableTracking = true;           ///< 是否启用跟踪
};

/**
 * @brief 高性能内存池管理器
 */
class TOAPI MemoryPool {
public:
    explicit MemoryPool(const MemoryPoolConfig& config);
    ~MemoryPool();
    
    // 禁用拷贝和移动
    MemoryPool(const MemoryPool&) = delete;
    MemoryPool& operator=(const MemoryPool&) = delete;
    MemoryPool(MemoryPool&&) = delete;
    MemoryPool& operator=(MemoryPool&&) = delete;
    
    /**
     * @brief 分配内存
     * @param size 请求的内存大小
     * @return 分配的内存指针，失败返回nullptr
     */
    [[nodiscard]] void* Allocate(size_t size) noexcept;
    
    /**
     * @brief 释放内存
     * @param ptr 要释放的内存指针
     * @return 是否释放成功
     */
    [[nodiscard]] bool Deallocate(void* ptr) noexcept;
    
    /**
     * @brief 获取内存池统计信息
     * @param allocatedBlocks 已分配的内存块数量
     * @param freeBlocks 空闲内存块数量
     * @param totalMemory 总内存大小
     * @return 是否获取成功
     */
    [[nodiscard]] bool GetStats(size_t& allocatedBlocks, size_t& freeBlocks, size_t& totalMemory) noexcept;
    
    /**
     * @brief 压缩内存池
     * @return 释放的内存大小
     */
    [[nodiscard]] size_t Compact() noexcept;
    
private:
    struct Block {
        void* data = nullptr;
        size_t size = 0;
        bool inUse = false;
        Block* next = nullptr;
    };
    
    MemoryPoolConfig m_config;
    std::mutex m_mutex;
    Block* m_freeList = nullptr;
    std::vector<std::unique_ptr<uint8_t[]>> m_chunks;
    size_t m_allocatedBlocks = 0;
    size_t m_totalBlocks = 0;
    
    bool AllocateNewChunk() noexcept;
};

// ==================== 异步任务调度器 ====================

/**
 * @brief 任务优先级
 */
enum class TaskPriority : uint32_t {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
};

/**
 * @brief 任务信息
 */
struct TaskInfo {
    uint64_t id = 0;                      ///< 任务ID
    std::wstring name;                    ///< 任务名称
    TaskPriority priority = TaskPriority::Normal; ///< 任务优先级
    std::function<void()> task;           ///< 任务函数
    std::chrono::steady_clock::time_point scheduledTime; ///< 调度时间
    
    bool operator<(const TaskInfo& other) const {
        if (priority != other.priority) {
            return priority < other.priority; // 高优先级在前
        }
        return scheduledTime > other.scheduledTime; // 早调度的在前
    }
};

/**
 * @brief 异步任务调度器
 */
class TOAPI TaskScheduler {
public:
    static TaskScheduler& Instance();
    
    // 禁用拷贝和移动
    TaskScheduler(const TaskScheduler&) = delete;
    TaskScheduler& operator=(const TaskScheduler&) = delete;
    TaskScheduler(TaskScheduler&&) = delete;
    TaskScheduler& operator=(TaskScheduler&&) = delete;
    
    /**
     * @brief 启动调度器
     * @param workerThreads 工作线程数量，0表示自动检测
     * @return 是否启动成功
     */
    [[nodiscard]] bool Start(uint32_t workerThreads = 0) noexcept;
    
    /**
     * @brief 停止调度器
     * @param waitForCompletion 是否等待所有任务完成
     * @return 是否停止成功
     */
    [[nodiscard]] bool Stop(bool waitForCompletion = true) noexcept;
    
    /**
     * @brief 提交任务
     * @param task 任务函数
     * @param priority 任务优先级
     * @param name 任务名称
     * @return 任务ID
     */
    [[nodiscard]] uint64_t SubmitTask(std::function<void()> task,
                                     TaskPriority priority = TaskPriority::Normal,
                                     const std::wstring& name = L"") noexcept;
    
    /**
     * @brief 提交延迟任务
     * @param task 任务函数
     * @param delayMs 延迟时间（毫秒）
     * @param priority 任务优先级
     * @param name 任务名称
     * @return 任务ID
     */
    [[nodiscard]] uint64_t SubmitDelayedTask(std::function<void()> task,
                                            uint32_t delayMs,
                                            TaskPriority priority = TaskPriority::Normal,
                                            const std::wstring& name = L"") noexcept;
    
    /**
     * @brief 取消任务
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    [[nodiscard]] bool CancelTask(uint64_t taskId) noexcept;
    
    /**
     * @brief 获取调度器统计信息
     * @param pendingTasks 待处理任务数量
     * @param completedTasks 已完成任务数量
     * @param workerThreads 工作线程数量
     * @return 是否获取成功
     */
    [[nodiscard]] bool GetStats(size_t& pendingTasks, size_t& completedTasks, size_t& workerThreads) noexcept;
    
private:
    TaskScheduler() = default;
    ~TaskScheduler();
    
    void WorkerThread() noexcept;
    
    std::atomic<bool> m_running{false};
    std::atomic<uint64_t> m_nextTaskId{1};
    std::atomic<size_t> m_completedTasks{0};
    
    std::mutex m_taskMutex;
    std::condition_variable m_taskCondition;
    std::priority_queue<TaskInfo> m_taskQueue;
    std::unordered_set<uint64_t> m_cancelledTasks;
    
    std::vector<std::thread> m_workers;
};

} // namespace HHBUI
