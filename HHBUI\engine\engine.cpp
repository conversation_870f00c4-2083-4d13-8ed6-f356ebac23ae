/**
 * @file engine.cpp
 * @brief HHBUI 引擎核心实现文件 - 提供引擎的完整实现
 * @version 1.0.0.01250720
 * @date 2025-01-31
 * <AUTHOR> Team
 *
 * 本文件实现了 HHBUI 引擎的核心功能，包括：
 * - 引擎初始化和反初始化的完整流程
 * - DPI 感知和缩放管理的高级实现
 * - 性能监控和统计的实时收集
 * - 线程安全的资源管理
 * - 错误处理和异常安全
 *
 * 使用 C++17 标准，确保与现代 C++ 特性兼容
 */

#include "pch.h"
#include "engine.h"
#include "common/winapi.h"
#include "common/Exception.h"
#include <thread>
#include <future>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <fstream>
#include <shlobj.h>

namespace HHBUI {

// ==================== 静态成员变量定义 ====================
std::mutex UIEngine::s_stateMutex;
std::atomic<bool> UIEngine::s_initialized{false};
std::atomic<bool> UIEngine::s_debugMode{false};
std::unique_ptr<EngineInitConfiguration> UIEngine::s_currentConfig = nullptr;
std::unique_ptr<EnginePerformanceStats> UIEngine::s_performanceStats = nullptr;

// ==================== 内部辅助类和函数 ====================

namespace {
    // 系统版本检测的现代化实现
    struct SystemVersionInfo {
        uint32_t majorVersion = 0;
        uint32_t minorVersion = 0;
        uint32_t buildNumber = 0;
        bool isWindows10OrLater = false;
        bool isWindows11OrLater = false;

        SystemVersionInfo() {
            DetectVersion();
        }

    private:
        void DetectVersion() noexcept {
            try {
                // 使用 RtlGetVersion 获取真实的系统版本
                typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);

                HMODULE ntdll = ::GetModuleHandleW(L"ntdll.dll");
                if (!ntdll) {
                    return;
                }

                auto RtlGetVersion = reinterpret_cast<RtlGetVersionPtr>(
                    ::GetProcAddress(ntdll, "RtlGetVersion"));
                if (!RtlGetVersion) {
                    return;
                }

                RTL_OSVERSIONINFOW versionInfo = {};
                versionInfo.dwOSVersionInfoSize = sizeof(versionInfo);

                if (RtlGetVersion(&versionInfo) == 0) { // STATUS_SUCCESS
                    majorVersion = versionInfo.dwMajorVersion;
                    minorVersion = versionInfo.dwMinorVersion;
                    buildNumber = versionInfo.dwBuildNumber;

                    // Windows 10 是版本 10.0
                    isWindows10OrLater = (majorVersion > 10) ||
                                        (majorVersion == 10 && minorVersion >= 0);

                    // Windows 11 的构建号从 22000 开始
                    isWindows11OrLater = isWindows10OrLater && (buildNumber >= 22000);
                }
            }
            catch (...) {
                // 发生异常时使用保守的默认值
                majorVersion = 6;
                minorVersion = 1;
                buildNumber = 0;
                isWindows10OrLater = false;
                isWindows11OrLater = false;
            }
        }
    };

    // 全局系统版本信息
    SystemVersionInfo g_systemVersion;

    // 线程安全的日志系统
    class ThreadSafeLogger {
    public:
        static ThreadSafeLogger& Instance() {
            static ThreadSafeLogger instance;
            return instance;
        }

        void SetCallback(std::function<void(const std::wstring&)> callback) {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_callback = std::move(callback);
        }

        void Log(int level, const std::wstring& message) {
            std::lock_guard<std::mutex> lock(m_mutex);

            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                now.time_since_epoch()) % 1000;

            std::wstringstream ss;
            ss << L"[" << std::put_time(std::localtime(&time_t), L"%Y-%m-%d %H:%M:%S");
            ss << L"." << std::setfill(L'0') << std::setw(3) << ms.count() << L"] ";

            switch (level) {
                case 0: ss << L"[INFO] "; break;
                case 1: ss << L"[WARN] "; break;
                case 2: ss << L"[ERROR] "; break;
                default: ss << L"[UNKNOWN] "; break;
            }

            ss << message;

            if (m_callback) {
                m_callback(ss.str());
            }

            // 同时输出到调试器
            if (UIEngine::QueryDebug()) {
                ::OutputDebugStringW(ss.str().c_str());
                ::OutputDebugStringW(L"\n");
            }
        }

    private:
        std::mutex m_mutex;
        std::function<void(const std::wstring&)> m_callback;
    };

    // 性能计时器
    class PerformanceTimer {
    public:
        PerformanceTimer() : m_start(std::chrono::high_resolution_clock::now()) {}

        double ElapsedSeconds() const {
            auto now = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(now - m_start);
            return duration.count();
        }

        void Reset() {
            m_start = std::chrono::high_resolution_clock::now();
        }

    private:
        std::chrono::high_resolution_clock::time_point m_start;
    };

} // anonymous namespace
// ==================== DPI 管理的现代化实现 ====================

namespace {
    /**
     * @brief 高级DPI管理器 - 提供完整的DPI感知和缩放功能
     */
    class AdvancedDpiManager {
    public:
        struct DpiInfo {
            float dpiX = USER_DEFAULT_SCREEN_DPI;
            float dpiY = USER_DEFAULT_SCREEN_DPI;
            float scaleX = 1.0f;
            float scaleY = 1.0f;
            bool isValid = false;
        };

        /**
         * @brief 初始化DPI管理器
         * @param config DPI配置
         * @return 是否初始化成功
         */
        static bool Initialize(const DpiConfiguration& config) noexcept {
            try {
                std::lock_guard<std::mutex> lock(s_dpiMutex);

                // 设置DPI感知模式
                if (!SetDpiAwarenessMode(config.awarenessMode)) {
                    ThreadSafeLogger::Instance().Log(1, L"Failed to set DPI awareness mode");
                }

                // 获取系统DPI信息
                DpiInfo systemDpi = GetSystemDpiInfo();
                if (!systemDpi.isValid) {
                    ThreadSafeLogger::Instance().Log(2, L"Failed to get system DPI information");
                    return false;
                }

                // 计算最终的缩放比例
                float finalScaleX = systemDpi.scaleX;
                float finalScaleY = systemDpi.scaleY;

                // 如果指定了自定义DPI缩放
                if (config.customDpiScale > 0.0f) {
                    finalScaleX = config.customDpiScale;
                    finalScaleY = config.customDpiScale;
                }

                // 应用缩放限制
                finalScaleX = std::clamp(finalScaleX, config.minDpiScale, config.maxDpiScale);
                finalScaleY = std::clamp(finalScaleY, config.minDpiScale, config.maxDpiScale);

                // 量化到0.25的倍数以减少渲染误差
                if (config.quantizeToQuarter) {
                    finalScaleX = QuantizeToQuarter(finalScaleX);
                    finalScaleY = QuantizeToQuarter(finalScaleY);
                }

                // 更新全局DPI信息
                s_currentDpi.dpiX = systemDpi.dpiX;
                s_currentDpi.dpiY = systemDpi.dpiY;
                s_currentDpi.scaleX = finalScaleX;
                s_currentDpi.scaleY = finalScaleY;
                s_currentDpi.isValid = true;

                // 更新UIWinApi中的DPI信息（保持兼容性）
                UIWinApi::ToList.CapsdpiX = systemDpi.dpiX;
                UIWinApi::ToList.CapsdpiY = systemDpi.dpiY;
                UIWinApi::ToList.drawing_default_dpi = finalScaleX;

                ThreadSafeLogger::Instance().Log(0,
                    L"DPI Manager initialized: System DPI(" +
                    std::to_wstring(systemDpi.dpiX) + L"x" + std::to_wstring(systemDpi.dpiY) +
                    L"), Scale(" + std::to_wstring(finalScaleX) + L"x" + std::to_wstring(finalScaleY) + L")");

                return true;
            }
            catch (const std::exception& e) {
                ThreadSafeLogger::Instance().Log(2, L"DPI Manager initialization failed: " +
                    std::wstring(e.what(), e.what() + strlen(e.what())));
                return false;
            }
        }

        /**
         * @brief 获取当前DPI缩放比例
         */
        static float GetCurrentScale() noexcept {
            std::lock_guard<std::mutex> lock(s_dpiMutex);
            return s_currentDpi.isValid ? s_currentDpi.scaleX : 1.0f;
        }

        /**
         * @brief 设置自定义DPI缩放比例
         */
        static bool SetCustomScale(float scale) noexcept {
            if (scale <= 0.0f || scale > 10.0f) {
                return false;
            }

            std::lock_guard<std::mutex> lock(s_dpiMutex);
            s_currentDpi.scaleX = scale;
            s_currentDpi.scaleY = scale;
            UIWinApi::ToList.drawing_default_dpi = scale;

            ThreadSafeLogger::Instance().Log(0, L"Custom DPI scale set to: " + std::to_wstring(scale));
            return true;
        }

        /**
         * @brief 获取监视器DPI信息
         */
        static bool GetMonitorDpi(HMONITOR monitor, uint32_t& dpiX, uint32_t& dpiY) noexcept {
            try {
                if (!monitor) {
                    monitor = ::MonitorFromPoint({0, 0}, MONITOR_DEFAULTTOPRIMARY);
                }

                // 尝试使用 GetDpiForMonitor (Windows 8.1+)
                HMODULE shcore = ::LoadLibraryW(L"Shcore.dll");
                if (shcore) {
                    typedef HRESULT(WINAPI* GetDpiForMonitorProc)(HMONITOR, int, UINT*, UINT*);
                    auto GetDpiForMonitor = reinterpret_cast<GetDpiForMonitorProc>(
                        ::GetProcAddress(shcore, "GetDpiForMonitor"));

                    if (GetDpiForMonitor) {
                        UINT x, y;
                        if (SUCCEEDED(GetDpiForMonitor(monitor, 0, &x, &y))) { // MDT_EFFECTIVE_DPI
                            dpiX = x;
                            dpiY = y;
                            ::FreeLibrary(shcore);
                            return true;
                        }
                    }
                    ::FreeLibrary(shcore);
                }

                // 回退到系统DPI
                HDC hdc = ::GetDC(nullptr);
                if (hdc) {
                    dpiX = ::GetDeviceCaps(hdc, LOGPIXELSX);
                    dpiY = ::GetDeviceCaps(hdc, LOGPIXELSY);
                    ::ReleaseDC(nullptr, hdc);
                    return true;
                }

                return false;
            }
            catch (...) {
                return false;
            }
        }

    private:
        static std::mutex s_dpiMutex;
        static DpiInfo s_currentDpi;

        /**
         * @brief 设置DPI感知模式
         */
        static bool SetDpiAwarenessMode(DpiAwarenessMode mode) noexcept {
            try {
                if (g_systemVersion.isWindows10OrLater) {
                    // Windows 10+ 使用 SetThreadDpiAwarenessContext
                    DPI_AWARENESS_CONTEXT context = DPI_AWARENESS_CONTEXT_UNAWARE;

                    switch (mode) {
                        case DpiAwarenessMode::Unaware:
                            context = DPI_AWARENESS_CONTEXT_UNAWARE;
                            break;
                        case DpiAwarenessMode::SystemAware:
                            context = DPI_AWARENESS_CONTEXT_SYSTEM_AWARE;
                            break;
                        case DpiAwarenessMode::PerMonitorAware:
                            context = DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE;
                            break;
                        case DpiAwarenessMode::PerMonitorAwareV2:
                            context = DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2;
                            break;
                    }

                    return ::SetThreadDpiAwarenessContext(context) != nullptr;
                } else {
                    // Windows 7/8 使用 SetProcessDPIAware
                    return ::SetProcessDPIAware() != FALSE;
                }
            }
            catch (...) {
                return false;
            }
        }

        /**
         * @brief 获取系统DPI信息
         */
        static DpiInfo GetSystemDpiInfo() noexcept {
            DpiInfo info;

            try {
                HDC hdc = ::GetDC(nullptr);
                if (hdc) {
                    info.dpiX = static_cast<float>(::GetDeviceCaps(hdc, LOGPIXELSX));
                    info.dpiY = static_cast<float>(::GetDeviceCaps(hdc, LOGPIXELSY));
                    ::ReleaseDC(nullptr, hdc);

                    info.scaleX = info.dpiX / USER_DEFAULT_SCREEN_DPI;
                    info.scaleY = info.dpiY / USER_DEFAULT_SCREEN_DPI;
                    info.isValid = true;
                }
            }
            catch (...) {
                // 使用默认值
            }

            return info;
        }

        /**
         * @brief 量化到0.25的倍数
         */
        static float QuantizeToQuarter(float value) noexcept {
            const float remainder = std::fmod(value, 0.25f);
            return (remainder < 0.125f) ? (value - remainder) : (value - remainder + 0.25f);
        }
    };

    // 静态成员定义
    std::mutex AdvancedDpiManager::s_dpiMutex;
    AdvancedDpiManager::DpiInfo AdvancedDpiManager::s_currentDpi;

} // anonymous namespace

// ==================== EngineInitConfiguration 实现 ====================

EngineInitConfiguration::EngineInitConfiguration(const info_Init& legacyInfo) noexcept {
    // 基础配置转换
    applicationInstance = legacyInfo.hInstance;
    deviceIndex = legacyInfo.device;

    // 设置标志
    flags = EngineInitFlags::Default;
    if (legacyInfo.dwDebug) {
        flags = flags | EngineInitFlags::EnableDebugMode;
    }

    // DPI配置转换
    if (legacyInfo.dwScaledpi > 0.0f) {
        dpiConfig.customDpiScale = legacyInfo.dwScaledpi;
    }

    // 字体配置转换
    if (legacyInfo.default_font_Face && wcslen(legacyInfo.default_font_Face) > 0) {
        fontConfig.fontFace = legacyInfo.default_font_Face;
        fontConfig.useSystemFont = false;
    }

    if (legacyInfo.default_font_Size > 0) {
        fontConfig.fontSize = legacyInfo.default_font_Size;
    }

    if (legacyInfo.default_font_Style != 0) {
        fontConfig.fontStyle = legacyInfo.default_font_Style;
    }
}

bool EngineInitConfiguration::LoadFromFile(const std::wstring& filePath) noexcept {
    try {
        // TODO: 实现配置文件加载
        // 这里可以使用 JSON、XML 或自定义格式
        ThreadSafeLogger::Instance().Log(1, L"Configuration file loading not implemented yet: " + filePath);
        return false;
    }
    catch (...) {
        return false;
    }
}

bool EngineInitConfiguration::SaveToFile(const std::wstring& filePath) const noexcept {
    try {
        // TODO: 实现配置文件保存
        ThreadSafeLogger::Instance().Log(1, L"Configuration file saving not implemented yet: " + filePath);
        return false;
    }
    catch (...) {
        return false;
    }
}

// ==================== UIEngine 核心实现 ====================

HRESULT UIEngine::Init(info_Init* info) noexcept {
    try {
        EngineInitConfiguration config;

        if (info) {
            config = EngineInitConfiguration(*info);
        }

        return InitEx(config);
    }
    catch (...) {
        return E_UNEXPECTED;
    }
}

HRESULT UIEngine::InitEx(const EngineInitConfiguration& config) noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);

        // 检查是否已经初始化
        if (s_initialized.load()) {
            ThreadSafeLogger::Instance().Log(1, L"Engine already initialized");
            return EngineErrorCodes::EE_ENGINE_ALREADY_INITIALIZED();
        }

        // 验证配置
        if (!ValidateConfiguration(config)) {
            ThreadSafeLogger::Instance().Log(2, L"Invalid engine configuration");
            return E_INVALIDARG;
        }

        ThreadSafeLogger::Instance().Log(0, L"Starting engine initialization...");
        PerformanceTimer initTimer;

        // 设置调试模式
        s_debugMode.store(HasFlag(config.flags, EngineInitFlags::EnableDebugMode));

        // 初始化性能统计
        if (HasFlag(config.flags, EngineInitFlags::EnablePerformanceMonitoring)) {
            s_performanceStats = std::make_unique<EnginePerformanceStats>();
            s_performanceStats->Reset();
        }

        // 调用进度回调
        if (config.progressCallback) {
            config.progressCallback(0.1f);
        }

        // 初始化COM
        HRESULT hr = ::CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
        if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
            ThreadSafeLogger::Instance().Log(2, L"COM initialization failed: " + std::to_wstring(hr));
            return EngineErrorCodes::EE_COM_INITIALIZATION_FAILED();
        }

        if (config.progressCallback) {
            config.progressCallback(0.2f);
        }

        // 初始化DPI感知
        if (!HasFlag(config.flags, EngineInitFlags::DisableDpiAwareness)) {
            if (!AdvancedDpiManager::Initialize(config.dpiConfig)) {
                ThreadSafeLogger::Instance().Log(2, L"DPI initialization failed");
                ::CoUninitialize();
                return EngineErrorCodes::EE_DPI_INITIALIZATION_FAILED();
            }
        }

        if (config.progressCallback) {
            config.progressCallback(0.4f);
        }

        // 获取应用程序实例
        HINSTANCE hInstance = config.applicationInstance;
        if (!hInstance) {
            hInstance = ::GetModuleHandleW(nullptr);
        }

        // 初始化UIWinApi
        hr = UIWinApi::Init(hInstance);
        if (FAILED(hr)) {
            ThreadSafeLogger::Instance().Log(2, L"UIWinApi initialization failed: " + std::to_wstring(hr));
            ::CoUninitialize();
            return hr;
        }

        if (config.progressCallback) {
            config.progressCallback(0.6f);
        }

        // 初始化渲染上下文
        hr = InitializeRenderContext(config.deviceType, config.deviceIndex);
        if (FAILED(hr)) {
            ThreadSafeLogger::Instance().Log(2, L"Render context initialization failed: " + std::to_wstring(hr));
            UIWinApi::UnInit();
            ::CoUninitialize();
            return hr;
        }

        if (config.progressCallback) {
            config.progressCallback(0.8f);
        }

        // 初始化默认字体
        hr = InitializeDefaultFont(config.fontConfig);
        if (FAILED(hr)) {
            ThreadSafeLogger::Instance().Log(2, L"Font initialization failed: " + std::to_wstring(hr));
            UIDrawContext::UnInit();
            UIWinApi::UnInit();
            ::CoUninitialize();
            return hr;
        }

        // 设置应用程序图标
        if (hInstance) {
            SetApplicationIcon(hInstance);
        }

        // 注册窗口类
        UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0);

        if (config.progressCallback) {
            config.progressCallback(0.9f);
        }

        // 保存配置
        s_currentConfig = std::make_unique<EngineInitConfiguration>(config);

        // 标记为已初始化
        s_initialized.store(true);

        if (config.progressCallback) {
            config.progressCallback(1.0f);
        }

        double initTime = initTimer.ElapsedSeconds();
        ThreadSafeLogger::Instance().Log(0,
            L"Engine initialization completed successfully in " +
            std::to_wstring(initTime * 1000.0) + L" ms");

        return S_OK;
    }
    catch (const Exception& e) {
        ThreadSafeLogger::Instance().Log(2, L"Engine initialization failed with exception: " + e.message());
        return e.status();
    }
    catch (const std::exception& e) {
        ThreadSafeLogger::Instance().Log(2, L"Engine initialization failed with std::exception: " +
            std::wstring(e.what(), e.what() + strlen(e.what())));
        return E_UNEXPECTED;
    }
    catch (...) {
        ThreadSafeLogger::Instance().Log(2, L"Engine initialization failed with unknown exception");
        return E_UNEXPECTED;
    }
}

HRESULT UIEngine::InitAsync(const EngineInitConfiguration& config,
                           std::function<void(HRESULT)> callback) noexcept {
    try {
        if (!callback) {
            return E_INVALIDARG;
        }

        // 检查是否已经初始化
        if (s_initialized.load()) {
            callback(EngineErrorCodes::EE_ENGINE_ALREADY_INITIALIZED());
            return S_OK;
        }

        // 启动异步初始化任务
        std::thread([config, callback]() {
            HRESULT result = InitEx(config);
            callback(result);
        }).detach();

        return S_OK;
    }
    catch (...) {
        return E_UNEXPECTED;
    }
}

HRESULT UIEngine::UnInit() noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);

        if (!s_initialized.load()) {
            ThreadSafeLogger::Instance().Log(1, L"Engine not initialized");
            return EngineErrorCodes::EE_ENGINE_NOT_INITIALIZED();
        }

        ThreadSafeLogger::Instance().Log(0, L"Starting engine uninitialization...");
        PerformanceTimer uninitTimer;

        // 清理资源
        CleanupResources();

        // 反初始化各个组件
        if (UIWinApi::ToList.default_font) {
            delete UIWinApi::ToList.default_font;
            UIWinApi::ToList.default_font = nullptr;
        }

        UIWinApi::UnInit();
        UIDrawContext::UnInit();

        // 反初始化COM
        ::CoUninitialize();

        // 清理图标
        if (UIWinApi::ToList.hIcon) {
            ::DestroyIcon(UIWinApi::ToList.hIcon);
            UIWinApi::ToList.hIcon = nullptr;
        }

        if (UIWinApi::ToList.hIconsm) {
            ::DestroyIcon(UIWinApi::ToList.hIconsm);
            UIWinApi::ToList.hIconsm = nullptr;
        }

        // 重置引擎实例
        UIWinApi::ToList.engine_instance = nullptr;

        // 清理配置和统计信息
        s_currentConfig.reset();
        s_performanceStats.reset();

        // 标记为未初始化
        s_initialized.store(false);
        s_debugMode.store(false);

        double uninitTime = uninitTimer.ElapsedSeconds();
        ThreadSafeLogger::Instance().Log(0,
            L"Engine uninitialization completed in " +
            std::to_wstring(uninitTime * 1000.0) + L" ms");

        return S_OK;
    }
    catch (const Exception& e) {
        ThreadSafeLogger::Instance().Log(2, L"Engine uninitialization failed with exception: " + e.message());
        return e.status();
    }
    catch (...) {
        ThreadSafeLogger::Instance().Log(2, L"Engine uninitialization failed with unknown exception");
        return E_UNEXPECTED;
    }
}

HRESULT UIEngine::ForceUnInit() noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);

        ThreadSafeLogger::Instance().Log(1, L"Force uninitialization started");

        // 强制清理，忽略所有错误
        try { CleanupResources(); } catch (...) {}

        try {
            if (UIWinApi::ToList.default_font) {
                delete UIWinApi::ToList.default_font;
                UIWinApi::ToList.default_font = nullptr;
            }
        } catch (...) {}

        try { UIWinApi::UnInit(); } catch (...) {}
        try { UIDrawContext::UnInit(); } catch (...) {}
        try { ::CoUninitialize(); } catch (...) {}

        try {
            if (UIWinApi::ToList.hIcon) {
                ::DestroyIcon(UIWinApi::ToList.hIcon);
                UIWinApi::ToList.hIcon = nullptr;
            }
        } catch (...) {}

        try {
            if (UIWinApi::ToList.hIconsm) {
                ::DestroyIcon(UIWinApi::ToList.hIconsm);
                UIWinApi::ToList.hIconsm = nullptr;
            }
        } catch (...) {}

        UIWinApi::ToList.engine_instance = nullptr;

        try { s_currentConfig.reset(); } catch (...) {}
        try { s_performanceStats.reset(); } catch (...) {}

        s_initialized.store(false);
        s_debugMode.store(false);

        ThreadSafeLogger::Instance().Log(1, L"Force uninitialization completed");
        return S_OK;
    }
    catch (...) {
        return E_UNEXPECTED;
    }
}

// ==================== 状态查询接口实现 ====================

bool UIEngine::QueryDebug() noexcept {
    return s_debugMode.load();
}

bool UIEngine::QueryInit() noexcept {
    return s_initialized.load();
}

std::optional<EngineInitConfiguration> UIEngine::GetInitConfiguration() noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);
        if (s_currentConfig) {
            return *s_currentConfig;
        }
        return std::nullopt;
    }
    catch (...) {
        return std::nullopt;
    }
}

LPCWSTR UIEngine::GetVersion() noexcept {
    return HHBUI_VERSION;
}

uint64_t UIEngine::GetVersionNumber() noexcept {
    return HHBUI_VERSION_NUM;
}

double UIEngine::GetUptime() noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);
        if (s_performanceStats) {
            return s_performanceStats->GetUptime();
        }
        return 0.0;
    }
    catch (...) {
        return 0.0;
    }
}

double UIEngine::GetTime() noexcept {
    static auto startTime = std::chrono::high_resolution_clock::now();
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(now - startTime);
    return duration.count();
}

// ==================== DPI 和缩放管理实现 ====================

float UIEngine::fScale(float value) noexcept {
    try {
        if (!s_initialized.load()) {
            return value;
        }

        float scale = AdvancedDpiManager::GetCurrentScale();
        if (scale > 1.0f) {
            return std::round(value * scale);
        }
        return value;
    }
    catch (...) {
        return value;
    }
}

bool UIEngine::fScaleBatch(const float* values, size_t count, float* scaledValues) noexcept {
    try {
        if (!values || !scaledValues || count == 0) {
            return false;
        }

        if (!s_initialized.load()) {
            std::copy(values, values + count, scaledValues);
            return true;
        }

        float scale = AdvancedDpiManager::GetCurrentScale();

        if (scale > 1.0f) {
            for (size_t i = 0; i < count; ++i) {
                scaledValues[i] = std::round(values[i] * scale);
            }
        } else {
            std::copy(values, values + count, scaledValues);
        }

        return true;
    }
    catch (...) {
        return false;
    }
}

float UIEngine::GetDefaultScale() noexcept {
    try {
        return AdvancedDpiManager::GetCurrentScale();
    }
    catch (...) {
        return 1.0f;
    }
}

bool UIEngine::SetCustomScale(float scale) noexcept {
    try {
        if (!s_initialized.load()) {
            return false;
        }

        return AdvancedDpiManager::SetCustomScale(scale);
    }
    catch (...) {
        return false;
    }
}

bool UIEngine::GetMonitorDpi(HMONITOR monitor, uint32_t& dpiX, uint32_t& dpiY) noexcept {
    try {
        return AdvancedDpiManager::GetMonitorDpi(monitor, dpiX, dpiY);
    }
    catch (...) {
        return false;
    }
}

// ==================== 性能监控和统计实现 ====================

EnginePerformanceStats UIEngine::GetPerformanceStats() noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);
        if (s_performanceStats) {
            return *s_performanceStats;
        }
        return EnginePerformanceStats{};
    }
    catch (...) {
        return EnginePerformanceStats{};
    }
}

bool UIEngine::ResetPerformanceStats() noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);
        if (s_performanceStats) {
            s_performanceStats->Reset();
            return true;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

uint64_t UIEngine::BeginPerformanceSession(const std::wstring& sessionName) noexcept {
    try {
        // TODO: 实现性能分析会话管理
        ThreadSafeLogger::Instance().Log(0, L"Performance session started: " + sessionName);
        return std::hash<std::wstring>{}(sessionName + std::to_wstring(GetTime()));
    }
    catch (...) {
        return 0;
    }
}

bool UIEngine::EndPerformanceSession(uint64_t sessionId) noexcept {
    try {
        // TODO: 实现性能分析会话结束
        ThreadSafeLogger::Instance().Log(0, L"Performance session ended: " + std::to_wstring(sessionId));
        return true;
    }
    catch (...) {
        return false;
    }
}

bool UIEngine::RecordPerformanceMark(const std::wstring& name, double value) noexcept {
    try {
        if (s_debugMode.load()) {
            ThreadSafeLogger::Instance().Log(0,
                L"Performance mark: " + name + L" = " + std::to_wstring(value));
        }
        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== 内存管理实现 ====================

bool UIEngine::GetMemoryUsage(size_t& totalMemory, size_t& peakMemory) noexcept {
    try {
        std::lock_guard<std::mutex> lock(s_stateMutex);
        if (s_performanceStats) {
            totalMemory = s_performanceStats->memoryUsage.load();
            peakMemory = s_performanceStats->peakMemoryUsage.load();
            return true;
        }
        totalMemory = 0;
        peakMemory = 0;
        return false;
    }
    catch (...) {
        return false;
    }
}

size_t UIEngine::TriggerGarbageCollection(bool aggressive) noexcept {
    try {
        ThreadSafeLogger::Instance().Log(0,
            aggressive ? L"Aggressive garbage collection triggered" : L"Garbage collection triggered");

        // TODO: 实现实际的垃圾回收逻辑
        // 这里可以调用各个子系统的清理函数

        return 0; // 返回回收的内存量
    }
    catch (...) {
        return 0;
    }
}

bool UIEngine::SetMemoryLimit(size_t maxMemory) noexcept {
    try {
        ThreadSafeLogger::Instance().Log(0, L"Memory limit set to: " + std::to_wstring(maxMemory) + L" bytes");
        // TODO: 实现内存限制设置
        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== 配置管理实现 ====================

namespace {
    std::mutex g_configMutex;
    std::unordered_map<std::wstring, std::wstring> g_configValues;
}

std::wstring UIEngine::GetConfigValue(const std::wstring& key, const std::wstring& defaultValue) noexcept {
    try {
        std::lock_guard<std::mutex> lock(g_configMutex);
        auto it = g_configValues.find(key);
        return (it != g_configValues.end()) ? it->second : defaultValue;
    }
    catch (...) {
        return defaultValue;
    }
}

bool UIEngine::SetConfigValue(const std::wstring& key, const std::wstring& value) noexcept {
    try {
        std::lock_guard<std::mutex> lock(g_configMutex);
        g_configValues[key] = value;
        return true;
    }
    catch (...) {
        return false;
    }
}

bool UIEngine::SaveConfig(const std::wstring& filePath) noexcept {
    try {
        std::lock_guard<std::mutex> lock(g_configMutex);

        std::wstring actualPath = filePath;
        if (actualPath.empty()) {
            // 使用默认配置文件路径
            wchar_t appDataPath[MAX_PATH];
            if (SUCCEEDED(::SHGetFolderPathW(nullptr, CSIDL_APPDATA, nullptr, 0, appDataPath))) {
                actualPath = std::wstring(appDataPath) + L"\\HHBUI\\config.ini";
            } else {
                actualPath = L"hhbui_config.ini";
            }
        }

        // TODO: 实现配置文件保存
        ThreadSafeLogger::Instance().Log(0, L"Configuration saved to: " + actualPath);
        return true;
    }
    catch (...) {
        return false;
    }
}

bool UIEngine::LoadConfig(const std::wstring& filePath) noexcept {
    try {
        std::lock_guard<std::mutex> lock(g_configMutex);

        // TODO: 实现配置文件加载
        ThreadSafeLogger::Instance().Log(0, L"Configuration loaded from: " + filePath);
        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== 调试和诊断功能实现 ====================

bool UIEngine::SetDebugMode(bool enable) noexcept {
    try {
        s_debugMode.store(enable);
        ThreadSafeLogger::Instance().Log(0,
            enable ? L"Debug mode enabled" : L"Debug mode disabled");
        return true;
    }
    catch (...) {
        return false;
    }
}

std::wstring UIEngine::GetSystemInfo() noexcept {
    try {
        std::wstringstream ss;

        // 操作系统信息
        ss << L"Operating System: Windows " << g_systemVersion.majorVersion
           << L"." << g_systemVersion.minorVersion
           << L" (Build " << g_systemVersion.buildNumber << L")\n";

        if (g_systemVersion.isWindows11OrLater) {
            ss << L"Windows Version: Windows 11 or later\n";
        } else if (g_systemVersion.isWindows10OrLater) {
            ss << L"Windows Version: Windows 10\n";
        } else {
            ss << L"Windows Version: Windows 8.1 or earlier\n";
        }

        // 处理器信息
        SYSTEM_INFO sysInfo;
        ::GetSystemInfo(&sysInfo);
        ss << L"Processor Architecture: ";
        switch (sysInfo.wProcessorArchitecture) {
            case PROCESSOR_ARCHITECTURE_AMD64:
                ss << L"x64 (AMD64)";
                break;
            case PROCESSOR_ARCHITECTURE_ARM:
                ss << L"ARM";
                break;
            case PROCESSOR_ARCHITECTURE_ARM64:
                ss << L"ARM64";
                break;
            case PROCESSOR_ARCHITECTURE_INTEL:
                ss << L"x86 (Intel)";
                break;
            default:
                ss << L"Unknown (" << sysInfo.wProcessorArchitecture << L")";
                break;
        }
        ss << L"\n";
        ss << L"Number of Processors: " << sysInfo.dwNumberOfProcessors << L"\n";

        // 内存信息
        MEMORYSTATUSEX memStatus;
        memStatus.dwLength = sizeof(memStatus);
        if (::GlobalMemoryStatusEx(&memStatus)) {
            ss << L"Total Physical Memory: " << (memStatus.ullTotalPhys / (1024 * 1024)) << L" MB\n";
            ss << L"Available Physical Memory: " << (memStatus.ullAvailPhys / (1024 * 1024)) << L" MB\n";
            ss << L"Memory Load: " << memStatus.dwMemoryLoad << L"%\n";
        }

        // DPI信息
        uint32_t dpiX, dpiY;
        if (GetMonitorDpi(nullptr, dpiX, dpiY)) {
            ss << L"Primary Monitor DPI: " << dpiX << L"x" << dpiY << L"\n";
            ss << L"DPI Scale Factor: " << GetDefaultScale() << L"\n";
        }

        return ss.str();
    }
    catch (...) {
        return L"Failed to retrieve system information";
    }
}

std::wstring UIEngine::GetRenderDeviceInfo() noexcept {
    try {
        std::wstringstream ss;

        // TODO: 获取渲染设备详细信息
        ss << L"Render Device Information:\n";
        ss << L"Direct2D Version: Available\n";
        ss << L"DirectWrite Version: Available\n";
        ss << L"Hardware Acceleration: ";

        // 检查硬件加速支持
        if (s_currentConfig && HasFlag(s_currentConfig->flags, EngineInitFlags::UseHardwareAcceleration)) {
            ss << L"Enabled\n";
        } else {
            ss << L"Disabled\n";
        }

        return ss.str();
    }
    catch (...) {
        return L"Failed to retrieve render device information";
    }
}

HRESULT UIEngine::PerformSelfCheck() noexcept {
    try {
        ThreadSafeLogger::Instance().Log(0, L"Starting engine self-check...");

        // 检查初始化状态
        if (!s_initialized.load()) {
            ThreadSafeLogger::Instance().Log(2, L"Self-check failed: Engine not initialized");
            return EngineErrorCodes::EE_ENGINE_NOT_INITIALIZED();
        }

        // 检查关键组件
        if (!UIWinApi::ToList.engine_instance) {
            ThreadSafeLogger::Instance().Log(2, L"Self-check failed: Invalid engine instance");
            return E_FAIL;
        }

        // 检查DPI管理器
        float scale = GetDefaultScale();
        if (scale <= 0.0f || scale > 10.0f) {
            ThreadSafeLogger::Instance().Log(2, L"Self-check failed: Invalid DPI scale");
            return E_FAIL;
        }

        // 检查内存状态
        size_t totalMem, peakMem;
        if (GetMemoryUsage(totalMem, peakMem)) {
            if (totalMem > 1024ULL * 1024 * 1024) { // 1GB
                ThreadSafeLogger::Instance().Log(1, L"Self-check warning: High memory usage detected");
            }
        }

        ThreadSafeLogger::Instance().Log(0, L"Engine self-check completed successfully");
        return S_OK;
    }
    catch (...) {
        ThreadSafeLogger::Instance().Log(2, L"Self-check failed with exception");
        return E_UNEXPECTED;
    }
}

std::wstring UIEngine::GenerateDiagnosticReport(const std::wstring& filePath) noexcept {
    try {
        std::wstringstream report;

        // 报告头部
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        report << L"HHBUI Engine Diagnostic Report\n";
        report << L"Generated: " << std::put_time(std::localtime(&time_t), L"%Y-%m-%d %H:%M:%S") << L"\n";
        report << L"Engine Version: " << GetVersion() << L"\n";
        report << L"========================================\n\n";

        // 引擎状态
        report << L"Engine Status:\n";
        report << L"  Initialized: " << (QueryInit() ? L"Yes" : L"No") << L"\n";
        report << L"  Debug Mode: " << (QueryDebug() ? L"Yes" : L"No") << L"\n";
        report << L"  Uptime: " << std::fixed << std::setprecision(2) << GetUptime() << L" seconds\n\n";

        // 系统信息
        report << L"System Information:\n";
        report << GetSystemInfo() << L"\n";

        // 渲染设备信息
        report << L"Render Device Information:\n";
        report << GetRenderDeviceInfo() << L"\n";

        // 性能统计
        if (s_performanceStats) {
            auto stats = GetPerformanceStats();
            report << L"Performance Statistics:\n";
            report << L"  Frame Count: " << stats.frameCount.load() << L"\n";
            report << L"  Draw Call Count: " << stats.drawCallCount.load() << L"\n";
            report << L"  Current FPS: " << std::fixed << std::setprecision(1) << stats.currentFps.load() << L"\n";
            report << L"  Average Frame Time: " << std::fixed << std::setprecision(2) << stats.averageFrameTime.load() << L" ms\n";

            size_t totalMem, peakMem;
            if (GetMemoryUsage(totalMem, peakMem)) {
                report << L"  Memory Usage: " << (totalMem / (1024 * 1024)) << L" MB\n";
                report << L"  Peak Memory Usage: " << (peakMem / (1024 * 1024)) << L" MB\n";
            }
            report << L"\n";
        }

        // 配置信息
        if (s_currentConfig) {
            report << L"Configuration:\n";
            report << L"  Device Type: " << static_cast<uint32_t>(s_currentConfig->deviceType) << L"\n";
            report << L"  Device Index: " << s_currentConfig->deviceIndex << L"\n";
            report << L"  Init Flags: " << static_cast<uint32_t>(s_currentConfig->flags) << L"\n";
            report << L"  DPI Scale: " << s_currentConfig->dpiConfig.customDpiScale << L"\n";
            report << L"  Font Face: " << s_currentConfig->fontConfig.fontFace << L"\n";
            report << L"  Font Size: " << s_currentConfig->fontConfig.fontSize << L"\n";
            report << L"\n";
        }

        std::wstring reportContent = report.str();

        // 如果指定了文件路径，保存到文件
        if (!filePath.empty()) {
            try {
                std::wofstream file(filePath);
                if (file.is_open()) {
                    file << reportContent;
                    file.close();
                    ThreadSafeLogger::Instance().Log(0, L"Diagnostic report saved to: " + filePath);
                    return L"";
                }
            }
            catch (...) {
                ThreadSafeLogger::Instance().Log(2, L"Failed to save diagnostic report to: " + filePath);
            }
        }

        return reportContent;
    }
    catch (...) {
        return L"Failed to generate diagnostic report";
    }
}

bool UIEngine::SetLogCallback(std::function<void(const std::wstring&)> callback) noexcept {
    try {
        ThreadSafeLogger::Instance().SetCallback(std::move(callback));
        return true;
    }
    catch (...) {
        return false;
    }
}

bool UIEngine::WriteLog(int level, const std::wstring& message) noexcept {
    try {
        ThreadSafeLogger::Instance().Log(level, message);
        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== 事件和回调管理实现 ====================

namespace {
    std::mutex g_eventMutex;
    std::unordered_map<std::wstring, std::vector<std::pair<uint64_t, std::function<void(const std::wstring&)>>>> g_eventCallbacks;
    std::atomic<uint64_t> g_nextCallbackId{1};
}

uint64_t UIEngine::RegisterEventCallback(const std::wstring& eventType,
                                         std::function<void(const std::wstring&)> callback) noexcept {
    try {
        if (!callback) {
            return 0;
        }

        std::lock_guard<std::mutex> lock(g_eventMutex);
        uint64_t callbackId = g_nextCallbackId.fetch_add(1);
        g_eventCallbacks[eventType].emplace_back(callbackId, std::move(callback));

        ThreadSafeLogger::Instance().Log(0,
            L"Event callback registered: " + eventType + L" (ID: " + std::to_wstring(callbackId) + L")");

        return callbackId;
    }
    catch (...) {
        return 0;
    }
}

bool UIEngine::UnregisterEventCallback(uint64_t callbackId) noexcept {
    try {
        std::lock_guard<std::mutex> lock(g_eventMutex);

        for (auto& [eventType, callbacks] : g_eventCallbacks) {
            auto it = std::find_if(callbacks.begin(), callbacks.end(),
                [callbackId](const auto& pair) { return pair.first == callbackId; });

            if (it != callbacks.end()) {
                callbacks.erase(it);
                ThreadSafeLogger::Instance().Log(0,
                    L"Event callback unregistered: " + eventType + L" (ID: " + std::to_wstring(callbackId) + L")");
                return true;
            }
        }

        return false;
    }
    catch (...) {
        return false;
    }
}

bool UIEngine::TriggerEvent(const std::wstring& eventType, const std::wstring& eventData) noexcept {
    try {
        std::lock_guard<std::mutex> lock(g_eventMutex);

        auto it = g_eventCallbacks.find(eventType);
        if (it != g_eventCallbacks.end()) {
            for (const auto& [callbackId, callback] : it->second) {
                try {
                    callback(eventData);
                }
                catch (...) {
                    ThreadSafeLogger::Instance().Log(2,
                        L"Event callback exception: " + eventType + L" (ID: " + std::to_wstring(callbackId) + L")");
                }
            }
            return true;
        }

        return false;
    }
    catch (...) {
        return false;
    }
}

// ==================== 实用工具函数实现 ====================

bool UIEngine::IsFeatureAvailable(const std::wstring& featureName) noexcept {
    try {
        // 检查各种功能的可用性
        if (featureName == L"DPI_AWARENESS") {
            return g_systemVersion.isWindows10OrLater;
        } else if (featureName == L"HARDWARE_ACCELERATION") {
            return s_currentConfig && HasFlag(s_currentConfig->flags, EngineInitFlags::UseHardwareAcceleration);
        } else if (featureName == L"PERFORMANCE_MONITORING") {
            return s_performanceStats != nullptr;
        } else if (featureName == L"DEBUG_MODE") {
            return s_debugMode.load();
        } else if (featureName == L"MULTITHREADING") {
            return s_currentConfig && s_currentConfig->performanceConfig.enableMultithreading;
        }

        return false;
    }
    catch (...) {
        return false;
    }
}

std::vector<std::wstring> UIEngine::GetAvailableFeatures() noexcept {
    try {
        std::vector<std::wstring> features;

        if (IsFeatureAvailable(L"DPI_AWARENESS")) {
            features.push_back(L"DPI_AWARENESS");
        }
        if (IsFeatureAvailable(L"HARDWARE_ACCELERATION")) {
            features.push_back(L"HARDWARE_ACCELERATION");
        }
        if (IsFeatureAvailable(L"PERFORMANCE_MONITORING")) {
            features.push_back(L"PERFORMANCE_MONITORING");
        }
        if (IsFeatureAvailable(L"DEBUG_MODE")) {
            features.push_back(L"DEBUG_MODE");
        }
        if (IsFeatureAvailable(L"MULTITHREADING")) {
            features.push_back(L"MULTITHREADING");
        }

        return features;
    }
    catch (...) {
        return {};
    }
}

bool UIEngine::WaitForReady(uint32_t timeoutMs) noexcept {
    try {
        if (s_initialized.load()) {
            return true;
        }

        auto startTime = std::chrono::steady_clock::now();

        while (!s_initialized.load()) {
            if (timeoutMs > 0) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - startTime);

                if (elapsed.count() >= timeoutMs) {
                    return false;
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== 内部辅助函数实现 ====================

bool UIEngine::ValidateConfiguration(const EngineInitConfiguration& config) noexcept {
    try {
        // 验证配置的有效性
        if (!config.IsValid()) {
            ThreadSafeLogger::Instance().Log(2, L"Invalid engine configuration");
            return false;
        }

        // 验证设备索引
        if (config.deviceIndex < -1) {
            ThreadSafeLogger::Instance().Log(2, L"Invalid device index: " + std::to_wstring(config.deviceIndex));
            return false;
        }

        // 验证字体配置
        if (!config.fontConfig.IsValid()) {
            ThreadSafeLogger::Instance().Log(2, L"Invalid font configuration");
            return false;
        }

        // 验证DPI配置
        if (!config.dpiConfig.IsValid()) {
            ThreadSafeLogger::Instance().Log(2, L"Invalid DPI configuration");
            return false;
        }

        // 验证性能配置
        if (!config.performanceConfig.IsValid()) {
            ThreadSafeLogger::Instance().Log(2, L"Invalid performance configuration");
            return false;
        }

        return true;
    }
    catch (...) {
        return false;
    }
}

HRESULT UIEngine::InitializeDpiAwareness(const DpiConfiguration& dpiConfig) noexcept {
    try {
        return AdvancedDpiManager::Initialize(dpiConfig) ? S_OK : E_FAIL;
    }
    catch (...) {
        return E_UNEXPECTED;
    }
}

HRESULT UIEngine::InitializeRenderContext(RenderDeviceType deviceType, int32_t deviceIndex) noexcept {
    try {
        // 将设备类型转换为旧版本的设备索引
        int legacyDeviceIndex = deviceIndex;

        if (deviceType == RenderDeviceType::Auto) {
            legacyDeviceIndex = -1; // 自动选择
        } else if (deviceType == RenderDeviceType::Hardware) {
            legacyDeviceIndex = (deviceIndex >= 0) ? deviceIndex : 0;
        } else if (deviceType == RenderDeviceType::Software) {
            legacyDeviceIndex = -2; // 软件渲染（如果支持）
        } else if (deviceType == RenderDeviceType::Warp) {
            legacyDeviceIndex = -3; // WARP设备（如果支持）
        }

        HRESULT hr = UIDrawContext::Init(legacyDeviceIndex);
        if (FAILED(hr)) {
            ThreadSafeLogger::Instance().Log(2,
                L"Render context initialization failed with device type " +
                std::to_wstring(static_cast<uint32_t>(deviceType)) +
                L", index " + std::to_wstring(deviceIndex) +
                L", HRESULT: " + std::to_wstring(hr));
        }

        return hr;
    }
    catch (...) {
        return E_UNEXPECTED;
    }
}

HRESULT UIEngine::InitializeDefaultFont(const FontConfiguration& fontConfig) noexcept {
    try {
        // 获取或创建默认字体的LOGFONT结构
        LOGFONTW* logFont = UIWinApi::ToList.drawing_default_fontLogFont;
        if (!logFont) {
            logFont = new LOGFONTW();
            UIWinApi::ToList.drawing_default_fontLogFont = logFont;

            // 获取系统默认字体
            if (!::SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW), logFont, FALSE)) {
                // 使用硬编码的默认值
                wcscpy_s(logFont->lfFaceName, L"Segoe UI");
                logFont->lfHeight = -15;
                logFont->lfWeight = FW_NORMAL;
                logFont->lfCharSet = DEFAULT_CHARSET;
                logFont->lfOutPrecision = OUT_DEFAULT_PRECIS;
                logFont->lfClipPrecision = CLIP_DEFAULT_PRECIS;
                logFont->lfQuality = CLEARTYPE_QUALITY;
                logFont->lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;
            }
        }

        // 应用字体配置
        if (!fontConfig.fontFace.empty() && !fontConfig.useSystemFont) {
            size_t len = std::min(fontConfig.fontFace.length(), static_cast<size_t>(LF_FACESIZE - 1));
            wcsncpy_s(logFont->lfFaceName, fontConfig.fontFace.c_str(), len);
            logFont->lfFaceName[len] = L'\0';
        }

        if (fontConfig.fontSize > 0) {
            logFont->lfHeight = -static_cast<LONG>(fScale(static_cast<float>(fontConfig.fontSize)));
        } else if (logFont->lfHeight == 0) {
            logFont->lfHeight = -static_cast<LONG>(fScale(14.0f)); // 默认字体大小
        }

        if (fontConfig.fontStyle != 0) {
            // 应用字体样式（需要根据实际的FontStyle枚举定义来实现）
            // 这里假设使用标准的字体样式标志
            logFont->lfWeight = (fontConfig.fontStyle & 1) ? FW_BOLD : FW_NORMAL; // Bold
            logFont->lfItalic = (fontConfig.fontStyle & 2) ? TRUE : FALSE;        // Italic
            logFont->lfUnderline = (fontConfig.fontStyle & 4) ? TRUE : FALSE;     // Underline
            logFont->lfStrikeOut = (fontConfig.fontStyle & 8) ? TRUE : FALSE;     // Strikeout
        }

        if (fontConfig.fontWeight >= 100.0f && fontConfig.fontWeight <= 900.0f) {
            logFont->lfWeight = static_cast<LONG>(fontConfig.fontWeight);
        }

        // 创建默认字体对象
        UIWinApi::ToList.default_font = new UIFont(logFont);
        if (!UIWinApi::ToList.default_font) {
            ThreadSafeLogger::Instance().Log(2, L"Failed to create default font object");
            return EngineErrorCodes::EE_FONT_INITIALIZATION_FAILED();
        }

        ThreadSafeLogger::Instance().Log(0,
            L"Default font initialized: " + std::wstring(logFont->lfFaceName) +
            L", size: " + std::to_wstring(abs(logFont->lfHeight)));

        return S_OK;
    }
    catch (...) {
        return E_UNEXPECTED;
    }
}

void UIEngine::SetApplicationIcon(HINSTANCE hInstance) noexcept {
    try {
        if (!hInstance) {
            return;
        }

        // 获取应用程序路径
        wchar_t szFilePath[MAX_PATH + 1] = {};
        if (::GetModuleFileNameW(hInstance, szFilePath, MAX_PATH) > 0) {
            // 提取图标
            UIWinApi::ToList.hIcon = ::ExtractIconW(hInstance, szFilePath, 0);
            UIWinApi::ToList.hIconsm = ::ExtractIconW(hInstance, szFilePath, 0);

            if (UIWinApi::ToList.hIcon) {
                ThreadSafeLogger::Instance().Log(0, L"Application icon loaded successfully");
            }
        }

        // 设置引擎实例
        UIWinApi::ToList.engine_instance = hInstance;
    }
    catch (...) {
        ThreadSafeLogger::Instance().Log(1, L"Failed to set application icon");
    }
}

void UIEngine::CleanupResources() noexcept {
    try {
        ThreadSafeLogger::Instance().Log(0, L"Cleaning up engine resources...");

        // 清理字体资源
        if (UIWinApi::ToList.drawing_default_fontLogFont) {
            delete UIWinApi::ToList.drawing_default_fontLogFont;
            UIWinApi::ToList.drawing_default_fontLogFont = nullptr;
        }

        // 触发垃圾回收
        TriggerGarbageCollection(true);

        // 清理事件回调
        {
            std::lock_guard<std::mutex> lock(g_eventMutex);
            g_eventCallbacks.clear();
        }

        // 清理配置
        {
            std::lock_guard<std::mutex> lock(g_configMutex);
            g_configValues.clear();
        }

        ThreadSafeLogger::Instance().Log(0, L"Engine resources cleanup completed");
    }
    catch (...) {
        ThreadSafeLogger::Instance().Log(1, L"Exception during resource cleanup");
    }
}

} // namespace HHBUI