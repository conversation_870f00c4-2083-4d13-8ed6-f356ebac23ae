﻿/**
 * @file engine.h
 * @brief HHBUI 引擎核心头文件 - 提供引擎初始化、配置管理和核心功能接口
 * @version 1.0.0.01250720
 * @date 2025-01-31
 * <AUTHOR> Team
 *
 * 本文件定义了 HHBUI 引擎的核心接口，包括：
 * - 引擎初始化和反初始化
 * - DPI 感知和缩放管理
 * - 调试和诊断功能
 * - 性能监控和分析
 * - 资源管理和配置
 *
 * 使用 C++17 标准，确保与现代 C++ 特性兼容
 */

#pragma once

#include <memory>
#include <string>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <optional>
#include <unordered_map>

namespace HHBUI {

// ==================== 前向声明 ====================
class UIFont;
class UIWinApi;
class UIDrawContext;

// ==================== 错误码定义 ====================
namespace EngineErrorCodes {
    constexpr HRESULT EE_ENGINE_ALREADY_INITIALIZED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x01);
    constexpr HRESULT EE_ENGINE_NOT_INITIALIZED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x02);
    constexpr HRESULT EE_INVALID_DEVICE_INDEX = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x03);
    constexpr HRESULT EE_DPI_INITIALIZATION_FAILED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x04);
    constexpr HRESULT EE_FONT_INITIALIZATION_FAILED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x05);
    constexpr HRESULT EE_RENDER_CONTEXT_FAILED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x06);
    constexpr HRESULT EE_COM_INITIALIZATION_FAILED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x07);
    constexpr HRESULT EE_SYSTEM_VERSION_UNSUPPORTED = _EX_MAKE_HRESULT(SEVERITY_ERROR, 0x01, 0x08);
}

// ==================== 枚举定义 ====================

/**
 * @brief 引擎初始化标志
 */
enum class EngineInitFlags : uint32_t {
    None = 0x00000000,                    ///< 无特殊标志
    EnableDebugMode = 0x00000001,         ///< 启用调试模式
    EnablePerformanceMonitoring = 0x00000002, ///< 启用性能监控
    EnableMemoryTracking = 0x00000004,    ///< 启用内存跟踪
    EnableAsyncInitialization = 0x00000008, ///< 启用异步初始化
    DisableDpiAwareness = 0x00000010,     ///< 禁用DPI感知
    UseHardwareAcceleration = 0x00000020, ///< 使用硬件加速
    EnableDetailedLogging = 0x00000040,   ///< 启用详细日志
    Default = EnablePerformanceMonitoring | UseHardwareAcceleration ///< 默认标志组合
};

// 位运算操作符重载
constexpr EngineInitFlags operator|(EngineInitFlags a, EngineInitFlags b) noexcept {
    return static_cast<EngineInitFlags>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

constexpr EngineInitFlags operator&(EngineInitFlags a, EngineInitFlags b) noexcept {
    return static_cast<EngineInitFlags>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

constexpr bool HasFlag(EngineInitFlags flags, EngineInitFlags flag) noexcept {
    return (flags & flag) == flag;
}

/**
 * @brief DPI 感知模式
 */
enum class DpiAwarenessMode : uint32_t {
    Unaware = 0,                          ///< 不感知DPI
    SystemAware = 1,                      ///< 系统DPI感知
    PerMonitorAware = 2,                  ///< 每监视器DPI感知
    PerMonitorAwareV2 = 3                 ///< 每监视器DPI感知V2（推荐）
};

/**
 * @brief 渲染设备类型
 */
enum class RenderDeviceType : uint32_t {
    Auto = 0,                             ///< 自动选择最佳设备
    Hardware = 1,                         ///< 硬件加速设备
    Software = 2,                         ///< 软件渲染设备
    Warp = 3                              ///< WARP设备（软件模拟硬件）
};

// ==================== 结构体定义 ====================

/**
 * @brief 字体配置信息
 */
struct FontConfiguration {
    std::wstring fontFace = L"";          ///< 字体名称，空字符串表示使用系统默认
    int32_t fontSize = 0;                 ///< 字体大小，0表示使用系统默认
    uint32_t fontStyle = 0;               ///< 字体样式，参考 FontStyle 枚举
    float fontWeight = 400.0f;            ///< 字体粗细 (100-900)
    bool useSystemFont = true;            ///< 是否使用系统字体作为后备

    /**
     * @brief 验证字体配置的有效性
     * @return 配置是否有效
     */
    [[nodiscard]] bool IsValid() const noexcept {
        return fontSize >= 0 && fontSize <= 1000 &&
               fontWeight >= 100.0f && fontWeight <= 900.0f;
    }

    /**
     * @brief 重置为默认配置
     */
    void Reset() noexcept {
        fontFace.clear();
        fontSize = 0;
        fontStyle = 0;
        fontWeight = 400.0f;
        useSystemFont = true;
    }
};

/**
 * @brief DPI 配置信息
 */
struct DpiConfiguration {
    float customDpiScale = 0.0f;          ///< 自定义DPI缩放，0表示使用系统DPI
    DpiAwarenessMode awarenessMode = DpiAwarenessMode::PerMonitorAwareV2; ///< DPI感知模式
    bool enableDpiScaling = true;         ///< 是否启用DPI缩放
    float minDpiScale = 0.5f;             ///< 最小DPI缩放比例
    float maxDpiScale = 4.0f;             ///< 最大DPI缩放比例
    bool quantizeToQuarter = true;        ///< 是否量化到0.25的倍数

    /**
     * @brief 验证DPI配置的有效性
     * @return 配置是否有效
     */
    [[nodiscard]] bool IsValid() const noexcept {
        return minDpiScale > 0.0f && maxDpiScale > minDpiScale &&
               minDpiScale <= 10.0f && maxDpiScale <= 10.0f &&
               (customDpiScale == 0.0f || (customDpiScale >= minDpiScale && customDpiScale <= maxDpiScale));
    }

    /**
     * @brief 重置为默认配置
     */
    void Reset() noexcept {
        customDpiScale = 0.0f;
        awarenessMode = DpiAwarenessMode::PerMonitorAwareV2;
        enableDpiScaling = true;
        minDpiScale = 0.5f;
        maxDpiScale = 4.0f;
        quantizeToQuarter = true;
    }
};

/**
 * @brief 性能配置信息
 */
struct PerformanceConfiguration {
    bool enableVSync = true;              ///< 启用垂直同步
    uint32_t targetFrameRate = 60;       ///< 目标帧率
    bool enableMultithreading = true;    ///< 启用多线程渲染
    uint32_t workerThreadCount = 0;      ///< 工作线程数量，0表示自动检测
    size_t memoryPoolSize = 64 * 1024 * 1024; ///< 内存池大小（字节）
    bool enableMemoryCompaction = true;  ///< 启用内存压缩

    /**
     * @brief 验证性能配置的有效性
     * @return 配置是否有效
     */
    [[nodiscard]] bool IsValid() const noexcept {
        return targetFrameRate > 0 && targetFrameRate <= 1000 &&
               memoryPoolSize >= 1024 * 1024 && memoryPoolSize <= 1024ULL * 1024 * 1024;
    }

    /**
     * @brief 重置为默认配置
     */
    void Reset() noexcept {
        enableVSync = true;
        targetFrameRate = 60;
        enableMultithreading = true;
        workerThreadCount = 0;
        memoryPoolSize = 64 * 1024 * 1024;
        enableMemoryCompaction = true;
    }
};

/**
 * @brief 引擎初始化配置信息 - 现代化的配置结构
 *
 * 这个结构体提供了完整的引擎初始化配置选项，包括：
 * - 渲染设备配置
 * - 字体和DPI配置
 * - 性能和调试选项
 * - 扩展配置支持
 */
struct EngineInitConfiguration {
    // ==================== 基础配置 ====================
    HINSTANCE applicationInstance = nullptr;  ///< 应用程序实例句柄，nullptr表示使用当前模块
    RenderDeviceType deviceType = RenderDeviceType::Auto; ///< 渲染设备类型
    int32_t deviceIndex = -1;                ///< 设备索引，-1表示自动选择
    EngineInitFlags flags = EngineInitFlags::Default; ///< 初始化标志

    // ==================== 字体和DPI配置 ====================
    FontConfiguration fontConfig;            ///< 字体配置
    DpiConfiguration dpiConfig;              ///< DPI配置

    // ==================== 性能配置 ====================
    PerformanceConfiguration performanceConfig; ///< 性能配置

    // ==================== 扩展配置 ====================
    std::wstring configFilePath = L"";       ///< 配置文件路径，空表示不使用配置文件
    std::wstring logFilePath = L"";          ///< 日志文件路径，空表示使用默认路径
    std::unordered_map<std::wstring, std::wstring> customProperties; ///< 自定义属性

    // ==================== 回调函数 ====================
    std::function<void(const std::wstring&)> logCallback = nullptr; ///< 日志回调函数
    std::function<void(float)> progressCallback = nullptr; ///< 初始化进度回调
    std::function<bool(const std::wstring&)> errorCallback = nullptr; ///< 错误处理回调，返回true继续，false中止

    /**
     * @brief 默认构造函数 - 使用推荐的默认配置
     */
    EngineInitConfiguration() = default;

    /**
     * @brief 兼容性构造函数 - 从旧版本的 info_Init 结构转换
     * @param legacyInfo 旧版本的初始化信息
     */
    explicit EngineInitConfiguration(const struct info_Init& legacyInfo) noexcept;

    /**
     * @brief 验证配置的完整性和有效性
     * @return 配置是否有效
     */
    [[nodiscard]] bool IsValid() const noexcept {
        return fontConfig.IsValid() &&
               dpiConfig.IsValid() &&
               performanceConfig.IsValid() &&
               deviceIndex >= -1;
    }

    /**
     * @brief 重置为默认配置
     */
    void Reset() noexcept {
        applicationInstance = nullptr;
        deviceType = RenderDeviceType::Auto;
        deviceIndex = -1;
        flags = EngineInitFlags::Default;
        fontConfig.Reset();
        dpiConfig.Reset();
        performanceConfig.Reset();
        configFilePath.clear();
        logFilePath.clear();
        customProperties.clear();
        logCallback = nullptr;
        progressCallback = nullptr;
        errorCallback = nullptr;
    }

    /**
     * @brief 从配置文件加载设置
     * @param filePath 配置文件路径
     * @return 是否成功加载
     */
    [[nodiscard]] bool LoadFromFile(const std::wstring& filePath) noexcept;

    /**
     * @brief 保存配置到文件
     * @param filePath 配置文件路径
     * @return 是否成功保存
     */
    [[nodiscard]] bool SaveToFile(const std::wstring& filePath) const noexcept;

    /**
     * @brief 获取自定义属性值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值
     */
    [[nodiscard]] std::wstring GetCustomProperty(const std::wstring& key,
                                                 const std::wstring& defaultValue = L"") const noexcept {
        auto it = customProperties.find(key);
        return (it != customProperties.end()) ? it->second : defaultValue;
    }

    /**
     * @brief 设置自定义属性值
     * @param key 属性键
     * @param value 属性值
     */
    void SetCustomProperty(const std::wstring& key, const std::wstring& value) {
        customProperties[key] = value;
    }
};

/**
 * @brief 旧版本兼容性结构体 - 保持向后兼容
 * @deprecated 建议使用 EngineInitConfiguration 替代
 */
struct info_Init {
    int device = -1;                      ///< 设备索引，-1表示自动选择
    HINSTANCE hInstance = nullptr;        ///< 应用程序实例句柄
    FLOAT dwScaledpi = 0.0f;             ///< DPI缩放值，0表示使用系统DPI
    BOOL dwDebug = FALSE;                ///< 是否启用调试模式
    LPCWSTR default_font_Face = nullptr; ///< 默认字体名称
    INT default_font_Size = 0;           ///< 默认字体大小
    DWORD default_font_Style = 0;        ///< 默认字体样式

    /**
     * @brief 转换为新版本配置结构
     * @return 新版本配置结构
     */
    [[nodiscard]] EngineInitConfiguration ToModernConfig() const noexcept {
        return EngineInitConfiguration(*this);
    }
};

// ==================== 性能统计结构 ====================

/**
 * @brief 引擎性能统计信息
 */
struct EnginePerformanceStats {
    std::chrono::steady_clock::time_point startTime; ///< 引擎启动时间
    std::atomic<uint64_t> frameCount{0};             ///< 总帧数
    std::atomic<uint64_t> drawCallCount{0};          ///< 绘制调用次数
    std::atomic<size_t> memoryUsage{0};              ///< 当前内存使用量（字节）
    std::atomic<size_t> peakMemoryUsage{0};          ///< 峰值内存使用量（字节）
    std::atomic<float> averageFrameTime{0.0f};       ///< 平均帧时间（毫秒）
    std::atomic<float> currentFps{0.0f};             ///< 当前FPS

    /**
     * @brief 重置统计信息
     */
    void Reset() noexcept {
        startTime = std::chrono::steady_clock::now();
        frameCount.store(0);
        drawCallCount.store(0);
        memoryUsage.store(0);
        peakMemoryUsage.store(0);
        averageFrameTime.store(0.0f);
        currentFps.store(0.0f);
    }

    /**
     * @brief 获取运行时间（秒）
     */
    [[nodiscard]] double GetUptime() const noexcept {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(now - startTime);
        return duration.count();
    }
};

// ==================== 主要引擎类 ====================

/**
 * @brief HHBUI 引擎核心类 - 提供引擎的完整生命周期管理
 *
 * UIEngine 是 HHBUI 框架的核心类，负责：
 * - 引擎的初始化和反初始化
 * - DPI 感知和缩放管理
 * - 性能监控和统计
 * - 资源管理和配置
 * - 调试和诊断功能
 *
 * 该类采用单例模式，确保全局只有一个引擎实例。
 * 所有方法都是线程安全的。
 *
 * @note 使用 C++17 标准，确保现代 C++ 特性支持
 * @warning 必须在主线程中调用 Init 和 UnInit 方法
 *
 * @example
 * ```cpp
 * // 基础初始化
 * HRESULT hr = UIEngine::Init();
 * if (SUCCEEDED(hr)) {
 *     // 使用引擎功能
 *     float scale = UIEngine::fScale(100.0f);
 *     // ...
 *     UIEngine::UnInit();
 * }
 *
 * // 高级初始化
 * EngineInitConfiguration config;
 * config.flags = EngineInitFlags::EnableDebugMode | EngineInitFlags::EnablePerformanceMonitoring;
 * config.dpiConfig.customDpiScale = 1.25f;
 * hr = UIEngine::InitEx(config);
 * ```
 */
class TOAPI UIEngine final {
public:
    // ==================== 禁用拷贝和移动 ====================
    UIEngine() = delete;
    UIEngine(const UIEngine&) = delete;
    UIEngine& operator=(const UIEngine&) = delete;
    UIEngine(UIEngine&&) = delete;
    UIEngine& operator=(UIEngine&&) = delete;

    // ==================== 核心生命周期管理 ====================

    /**
     * @brief 引擎初始化 - 兼容性接口
     * @param info 初始化信息，nullptr表示使用默认配置
     * @return 执行状态
     * @retval S_OK 初始化成功
     * @retval EE_ENGINE_ALREADY_INITIALIZED 引擎已经初始化
     * @retval EE_COM_INITIALIZATION_FAILED COM初始化失败
     * @retval EE_RENDER_CONTEXT_FAILED 渲染上下文创建失败
     *
     * @note 这是为了保持向后兼容性的接口，建议使用 InitEx
     * @warning 必须在主线程中调用
     */
    [[nodiscard]] static HRESULT Init(info_Init* info = nullptr) noexcept;

    /**
     * @brief 引擎高级初始化 - 推荐接口
     * @param config 详细的初始化配置
     * @return 执行状态
     * @retval S_OK 初始化成功
     * @retval EE_ENGINE_ALREADY_INITIALIZED 引擎已经初始化
     * @retval EE_COM_INITIALIZATION_FAILED COM初始化失败
     * @retval EE_RENDER_CONTEXT_FAILED 渲染上下文创建失败
     * @retval EE_INVALID_DEVICE_INDEX 无效的设备索引
     *
     * @note 这是推荐的初始化方法，提供完整的配置选项
     * @warning 必须在主线程中调用
     */
    [[nodiscard]] static HRESULT InitEx(const EngineInitConfiguration& config) noexcept;

    /**
     * @brief 异步引擎初始化
     * @param config 初始化配置
     * @param callback 完成回调函数，参数为初始化结果
     * @return 立即返回S_OK表示异步操作已开始，其他值表示无法开始异步操作
     *
     * @note 适用于需要在后台初始化引擎的场景
     * @warning 回调函数可能在任意线程中执行
     */
    [[nodiscard]] static HRESULT InitAsync(const EngineInitConfiguration& config,
                                          std::function<void(HRESULT)> callback) noexcept;

    /**
     * @brief 引擎反初始化
     * @return 执行状态
     * @retval S_OK 反初始化成功
     * @retval EE_ENGINE_NOT_INITIALIZED 引擎未初始化
     *
     * @note 会自动清理所有资源和释放内存
     * @warning 必须在主线程中调用，且确保没有其他线程在使用引擎功能
     */
    [[nodiscard]] static HRESULT UnInit() noexcept;

    /**
     * @brief 强制引擎反初始化
     * @return 执行状态
     *
     * @note 即使出现错误也会尽力清理资源
     * @warning 仅在紧急情况下使用，可能导致资源泄漏
     */
    [[nodiscard]] static HRESULT ForceUnInit() noexcept;

    // ==================== 状态查询接口 ====================

    /**
     * @brief 查询引擎是否处于调试模式
     * @return 是否为调试模式
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool QueryDebug() noexcept;

    /**
     * @brief 查询引擎是否已经初始化
     * @return 是否已初始化
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool QueryInit() noexcept;

    /**
     * @brief 查询引擎初始化状态
     * @return 初始化状态的详细信息
     *
     * @note 线程安全
     */
    [[nodiscard]] static std::optional<EngineInitConfiguration> GetInitConfiguration() noexcept;

    /**
     * @brief 获取引擎版本信息
     * @return 版本字符串
     *
     * @note 线程安全，返回的字符串在引擎生命周期内有效
     */
    [[nodiscard]] static LPCWSTR GetVersion() noexcept;

    /**
     * @brief 获取引擎版本号
     * @return 数值版本号
     *
     * @note 线程安全
     */
    [[nodiscard]] static uint64_t GetVersionNumber() noexcept;

    /**
     * @brief 获取引擎运行时间
     * @return 运行时间（秒）
     *
     * @note 线程安全，从引擎初始化开始计算
     */
    [[nodiscard]] static double GetUptime() noexcept;

    /**
     * @brief 获取高精度时间戳
     * @return 时间戳（秒，浮点数）
     *
     * @note 线程安全，适用于动画和性能测量
     */
    [[nodiscard]] static double GetTime() noexcept;

    // ==================== DPI 和缩放管理 ====================

    /**
     * @brief 计算DPI缩放值
     * @param value 原始值
     * @return 缩放后的值
     *
     * @note 线程安全，会根据当前DPI设置进行缩放
     */
    [[nodiscard]] static float fScale(float value) noexcept;

    /**
     * @brief 批量计算DPI缩放值
     * @param values 原始值数组
     * @param count 数组长度
     * @param scaledValues 输出缩放后的值数组
     * @return 是否成功
     *
     * @note 线程安全，适用于批量缩放操作
     */
    [[nodiscard]] static bool fScaleBatch(const float* values, size_t count, float* scaledValues) noexcept;

    /**
     * @brief 获取默认DPI缩放系数
     * @return DPI缩放系数
     *
     * @note 线程安全
     */
    [[nodiscard]] static float GetDefaultScale() noexcept;

    /**
     * @brief 设置自定义DPI缩放系数
     * @param scale 新的缩放系数
     * @return 是否设置成功
     *
     * @note 线程安全，会影响后续的缩放计算
     */
    [[nodiscard]] static bool SetCustomScale(float scale) noexcept;

    /**
     * @brief 获取当前监视器的DPI信息
     * @param monitor 监视器句柄，nullptr表示主监视器
     * @param dpiX 输出水平DPI
     * @param dpiY 输出垂直DPI
     * @return 是否获取成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool GetMonitorDpi(HMONITOR monitor, uint32_t& dpiX, uint32_t& dpiY) noexcept;

    // ==================== 性能监控和统计 ====================

    /**
     * @brief 获取引擎性能统计信息
     * @return 性能统计信息
     *
     * @note 线程安全，返回当前的性能统计快照
     */
    [[nodiscard]] static EnginePerformanceStats GetPerformanceStats() noexcept;

    /**
     * @brief 重置性能统计信息
     * @return 是否重置成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool ResetPerformanceStats() noexcept;

    /**
     * @brief 开始性能分析会话
     * @param sessionName 会话名称
     * @return 会话ID，用于后续操作
     *
     * @note 线程安全，可以同时运行多个分析会话
     */
    [[nodiscard]] static uint64_t BeginPerformanceSession(const std::wstring& sessionName) noexcept;

    /**
     * @brief 结束性能分析会话
     * @param sessionId 会话ID
     * @return 是否成功结束
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool EndPerformanceSession(uint64_t sessionId) noexcept;

    /**
     * @brief 记录性能标记
     * @param name 标记名称
     * @param value 标记值
     * @return 是否记录成功
     *
     * @note 线程安全，用于自定义性能监控点
     */
    [[nodiscard]] static bool RecordPerformanceMark(const std::wstring& name, double value) noexcept;

    // ==================== 内存管理 ====================

    /**
     * @brief 获取当前内存使用情况
     * @param totalMemory 输出总内存使用量（字节）
     * @param peakMemory 输出峰值内存使用量（字节）
     * @return 是否获取成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool GetMemoryUsage(size_t& totalMemory, size_t& peakMemory) noexcept;

    /**
     * @brief 触发内存垃圾回收
     * @param aggressive 是否进行激进回收
     * @return 回收的内存量（字节）
     *
     * @note 可能会暂时影响性能
     */
    [[nodiscard]] static size_t TriggerGarbageCollection(bool aggressive = false) noexcept;

    /**
     * @brief 设置内存使用限制
     * @param maxMemory 最大内存使用量（字节），0表示无限制
     * @return 是否设置成功
     *
     * @note 线程安全，超出限制时会自动触发垃圾回收
     */
    [[nodiscard]] static bool SetMemoryLimit(size_t maxMemory) noexcept;

    // ==================== 配置管理 ====================

    /**
     * @brief 获取配置值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     *
     * @note 线程安全
     */
    [[nodiscard]] static std::wstring GetConfigValue(const std::wstring& key,
                                                     const std::wstring& defaultValue = L"") noexcept;

    /**
     * @brief 设置配置值
     * @param key 配置键
     * @param value 配置值
     * @return 是否设置成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool SetConfigValue(const std::wstring& key, const std::wstring& value) noexcept;

    /**
     * @brief 保存配置到文件
     * @param filePath 文件路径，空字符串表示使用默认路径
     * @return 是否保存成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool SaveConfig(const std::wstring& filePath = L"") noexcept;

    /**
     * @brief 从文件加载配置
     * @param filePath 文件路径
     * @return 是否加载成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool LoadConfig(const std::wstring& filePath) noexcept;

    // ==================== 调试和诊断功能 ====================

    /**
     * @brief 启用或禁用调试模式
     * @param enable 是否启用
     * @return 是否设置成功
     *
     * @note 线程安全，调试模式会影响性能
     */
    [[nodiscard]] static bool SetDebugMode(bool enable) noexcept;

    /**
     * @brief 获取系统信息
     * @return 系统信息字符串
     *
     * @note 线程安全，包含操作系统、硬件、驱动等信息
     */
    [[nodiscard]] static std::wstring GetSystemInfo() noexcept;

    /**
     * @brief 获取渲染设备信息
     * @return 渲染设备信息字符串
     *
     * @note 线程安全
     */
    [[nodiscard]] static std::wstring GetRenderDeviceInfo() noexcept;

    /**
     * @brief 执行引擎自检
     * @return 自检结果，S_OK表示正常
     *
     * @note 可能会消耗一定时间，建议在非关键路径调用
     */
    [[nodiscard]] static HRESULT PerformSelfCheck() noexcept;

    /**
     * @brief 生成诊断报告
     * @param filePath 报告文件路径，空字符串表示返回字符串
     * @return 诊断报告内容或空字符串（如果保存到文件）
     *
     * @note 包含完整的引擎状态、性能统计、系统信息等
     */
    [[nodiscard]] static std::wstring GenerateDiagnosticReport(const std::wstring& filePath = L"") noexcept;

    /**
     * @brief 设置日志回调函数
     * @param callback 日志回调函数
     * @return 是否设置成功
     *
     * @note 线程安全，回调函数可能在任意线程中执行
     */
    [[nodiscard]] static bool SetLogCallback(std::function<void(const std::wstring&)> callback) noexcept;

    /**
     * @brief 写入日志消息
     * @param level 日志级别（0=信息，1=警告，2=错误）
     * @param message 日志消息
     * @return 是否写入成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool WriteLog(int level, const std::wstring& message) noexcept;

    // ==================== 事件和回调管理 ====================

    /**
     * @brief 注册引擎事件回调
     * @param eventType 事件类型
     * @param callback 回调函数
     * @return 回调ID，用于后续取消注册
     *
     * @note 线程安全
     */
    [[nodiscard]] static uint64_t RegisterEventCallback(const std::wstring& eventType,
                                                        std::function<void(const std::wstring&)> callback) noexcept;

    /**
     * @brief 取消注册事件回调
     * @param callbackId 回调ID
     * @return 是否取消成功
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool UnregisterEventCallback(uint64_t callbackId) noexcept;

    /**
     * @brief 触发引擎事件
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return 是否触发成功
     *
     * @note 线程安全，所有注册的回调都会被调用
     */
    [[nodiscard]] static bool TriggerEvent(const std::wstring& eventType, const std::wstring& eventData) noexcept;

    // ==================== 实用工具函数 ====================

    /**
     * @brief 检查功能是否可用
     * @param featureName 功能名称
     * @return 功能是否可用
     *
     * @note 线程安全
     */
    [[nodiscard]] static bool IsFeatureAvailable(const std::wstring& featureName) noexcept;

    /**
     * @brief 获取可用功能列表
     * @return 功能名称列表
     *
     * @note 线程安全
     */
    [[nodiscard]] static std::vector<std::wstring> GetAvailableFeatures() noexcept;

    /**
     * @brief 等待引擎就绪
     * @param timeoutMs 超时时间（毫秒），0表示无限等待
     * @return 是否在超时前就绪
     *
     * @note 线程安全，适用于异步初始化场景
     */
    [[nodiscard]] static bool WaitForReady(uint32_t timeoutMs = 0) noexcept;

private:
    // ==================== 内部实现 ====================

    // 私有构造函数，防止实例化
    UIEngine() = default;
    ~UIEngine() = default;

    // 内部初始化实现
    static HRESULT InternalInit(const EngineInitConfiguration& config) noexcept;
    static HRESULT InternalUnInit() noexcept;

    // 线程安全的状态管理
    static std::mutex s_stateMutex;
    static std::atomic<bool> s_initialized;
    static std::atomic<bool> s_debugMode;
    static std::unique_ptr<EngineInitConfiguration> s_currentConfig;
    static std::unique_ptr<EnginePerformanceStats> s_performanceStats;

    // 内部辅助函数
    static bool ValidateConfiguration(const EngineInitConfiguration& config) noexcept;
    static HRESULT InitializeDpiAwareness(const DpiConfiguration& dpiConfig) noexcept;
    static HRESULT InitializeRenderContext(RenderDeviceType deviceType, int32_t deviceIndex) noexcept;
    static HRESULT InitializeDefaultFont(const FontConfiguration& fontConfig) noexcept;
    static void SetApplicationIcon(HINSTANCE hInstance) noexcept;
    static void CleanupResources() noexcept;
};

// ==================== 全局辅助函数 ====================

/**
 * @brief 获取引擎实例（用于向后兼容）
 * @return 引擎类引用
 * @deprecated 直接使用 UIEngine 静态方法
 */
[[deprecated("Use UIEngine static methods directly")]]
inline UIEngine& GetEngineInstance() {
    static UIEngine instance;
    return instance;
}

/**
 * @brief RAII 引擎管理器 - 自动管理引擎生命周期
 */
class EngineManager {
public:
    explicit EngineManager(const EngineInitConfiguration& config = {})
        : m_initialized(false) {
        if (SUCCEEDED(UIEngine::InitEx(config))) {
            m_initialized = true;
        }
    }

    ~EngineManager() {
        if (m_initialized) {
            UIEngine::UnInit();
        }
    }

    // 禁用拷贝和移动
    EngineManager(const EngineManager&) = delete;
    EngineManager& operator=(const EngineManager&) = delete;
    EngineManager(EngineManager&&) = delete;
    EngineManager& operator=(EngineManager&&) = delete;

    [[nodiscard]] bool IsInitialized() const noexcept { return m_initialized; }

private:
    bool m_initialized;
};

} // namespace HHBUI
