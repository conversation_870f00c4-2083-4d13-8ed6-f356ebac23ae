/**
 * @file engine_advanced.cpp
 * @brief HHBUI 引擎高级功能实现文件
 * @version 1.0.0.01250720
 * @date 2025-01-31
 * <AUTHOR> Team
 */

#include "pch.h"
#include "engine_advanced.h"
#include "common/Exception.h"
#include <algorithm>
#include <fstream>

namespace HHBUI {

// ==================== ResourceManager 实现 ====================

ResourceManager& ResourceManager::Instance() {
    static ResourceManager instance;
    return instance;
}

bool ResourceManager::LoadResource(const std::wstring& name, 
                                  const std::wstring& path, 
                                  ResourceType type) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 检查资源是否已存在
        auto it = m_resources.find(name);
        if (it != m_resources.end()) {
            it->second->refCount++;
            return true;
        }
        
        // 创建新的资源信息
        auto resource = std::make_shared<ResourceInfo>();
        resource->name = name;
        resource->path = path;
        resource->type = type;
        
        // 获取文件信息
        WIN32_FILE_ATTRIBUTE_DATA fileData;
        if (::GetFileAttributesExW(path.c_str(), GetFileExInfoStandard, &fileData)) {
            LARGE_INTEGER fileSize;
            fileSize.LowPart = fileData.nFileSizeLow;
            fileSize.HighPart = fileData.nFileSizeHigh;
            resource->size = static_cast<size_t>(fileSize.QuadPart);
            
            FILETIME ft = fileData.ftLastWriteTime;
            resource->lastModified = (static_cast<uint64_t>(ft.dwHighDateTime) << 32) | ft.dwLowDateTime;
        }
        
        // TODO: 根据资源类型加载实际数据
        // 这里可以调用相应的加载函数
        
        resource->isLoaded = true;
        resource->refCount = 1;
        
        m_resources[name] = resource;
        
        UIEngine::WriteLog(0, L"Resource loaded: " + name + L" (" + path + L")");
        return true;
    }
    catch (...) {
        UIEngine::WriteLog(2, L"Failed to load resource: " + name);
        return false;
    }
}

bool ResourceManager::LoadResourceAsync(const std::wstring& name,
                                        const std::wstring& path,
                                        ResourceType type,
                                        std::function<void(bool)> callback) noexcept {
    try {
        if (!callback) {
            return false;
        }
        
        // 提交异步加载任务
        TaskScheduler::Instance().SubmitTask([this, name, path, type, callback]() {
            bool result = LoadResource(name, path, type);
            callback(result);
        }, TaskPriority::Normal, L"LoadResource_" + name);
        
        return true;
    }
    catch (...) {
        return false;
    }
}

std::shared_ptr<ResourceInfo> ResourceManager::GetResource(const std::wstring& name) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_resources.find(name);
        if (it != m_resources.end()) {
            it->second->refCount++;
            return it->second;
        }
        return nullptr;
    }
    catch (...) {
        return nullptr;
    }
}

bool ResourceManager::UnloadResource(const std::wstring& name) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_resources.find(name);
        if (it != m_resources.end()) {
            if (--it->second->refCount == 0) {
                m_resources.erase(it);
                UIEngine::WriteLog(0, L"Resource unloaded: " + name);
            }
            return true;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

size_t ResourceManager::CleanupUnusedResources() noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        size_t cleanedCount = 0;
        
        auto it = m_resources.begin();
        while (it != m_resources.end()) {
            if (it->second->refCount == 0) {
                UIEngine::WriteLog(0, L"Cleaning up unused resource: " + it->first);
                it = m_resources.erase(it);
                cleanedCount++;
            } else {
                ++it;
            }
        }
        
        return cleanedCount;
    }
    catch (...) {
        return 0;
    }
}

bool ResourceManager::GetResourceStats(size_t& totalCount, size_t& loadedCount, size_t& totalSize) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        totalCount = m_resources.size();
        loadedCount = 0;
        totalSize = 0;
        
        for (const auto& [name, resource] : m_resources) {
            if (resource->isLoaded) {
                loadedCount++;
            }
            totalSize += resource->size;
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== MemoryPool 实现 ====================

MemoryPool::MemoryPool(const MemoryPoolConfig& config) : m_config(config) {
    try {
        // 分配初始内存块
        if (!AllocateNewChunk()) {
            throw std::runtime_error("Failed to allocate initial memory chunk");
        }
        
        UIEngine::WriteLog(0, L"Memory pool created with " + 
                          std::to_wstring(m_config.initialBlocks) + L" blocks of " + 
                          std::to_wstring(m_config.blockSize) + L" bytes each");
    }
    catch (...) {
        UIEngine::WriteLog(2, L"Failed to create memory pool");
    }
}

MemoryPool::~MemoryPool() {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 清理所有内存块
        m_chunks.clear();
        m_freeList = nullptr;
        
        UIEngine::WriteLog(0, L"Memory pool destroyed");
    }
    catch (...) {
        UIEngine::WriteLog(1, L"Exception during memory pool destruction");
    }
}

void* MemoryPool::Allocate(size_t size) noexcept {
    try {
        if (size > m_config.blockSize) {
            UIEngine::WriteLog(1, L"Requested size exceeds block size: " + std::to_wstring(size));
            return nullptr;
        }
        
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 如果没有空闲块，尝试分配新的内存块
        if (!m_freeList && m_config.allowGrowth && m_totalBlocks < m_config.maxBlocks) {
            if (!AllocateNewChunk()) {
                return nullptr;
            }
        }
        
        if (!m_freeList) {
            UIEngine::WriteLog(1, L"Memory pool exhausted");
            return nullptr;
        }
        
        // 从空闲列表中取出一个块
        Block* block = m_freeList;
        m_freeList = block->next;
        block->inUse = true;
        block->next = nullptr;
        m_allocatedBlocks++;
        
        return block->data;
    }
    catch (...) {
        return nullptr;
    }
}

bool MemoryPool::Deallocate(void* ptr) noexcept {
    try {
        if (!ptr) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 查找对应的内存块
        for (const auto& chunk : m_chunks) {
            uint8_t* chunkStart = chunk.get();
            uint8_t* chunkEnd = chunkStart + (m_config.blockSize + sizeof(Block)) * m_config.initialBlocks;
            
            if (ptr >= chunkStart && ptr < chunkEnd) {
                // 计算块的位置
                size_t offset = static_cast<uint8_t*>(ptr) - chunkStart;
                size_t blockIndex = offset / (m_config.blockSize + sizeof(Block));
                
                Block* block = reinterpret_cast<Block*>(chunkStart + blockIndex * (m_config.blockSize + sizeof(Block)));
                
                if (block->inUse && block->data == ptr) {
                    // 将块返回到空闲列表
                    block->inUse = false;
                    block->next = m_freeList;
                    m_freeList = block;
                    m_allocatedBlocks--;
                    return true;
                }
            }
        }
        
        UIEngine::WriteLog(1, L"Invalid pointer passed to memory pool deallocate");
        return false;
    }
    catch (...) {
        return false;
    }
}

bool MemoryPool::GetStats(size_t& allocatedBlocks, size_t& freeBlocks, size_t& totalMemory) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        allocatedBlocks = m_allocatedBlocks;
        freeBlocks = m_totalBlocks - m_allocatedBlocks;
        totalMemory = m_totalBlocks * m_config.blockSize;
        
        return true;
    }
    catch (...) {
        return false;
    }
}

size_t MemoryPool::Compact() noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // TODO: 实现内存压缩逻辑
        // 这里可以重新整理内存块，减少碎片
        
        UIEngine::WriteLog(0, L"Memory pool compaction completed");
        return 0;
    }
    catch (...) {
        return 0;
    }
}

bool MemoryPool::AllocateNewChunk() noexcept {
    try {
        size_t chunkSize = (m_config.blockSize + sizeof(Block)) * m_config.initialBlocks;
        auto chunk = std::make_unique<uint8_t[]>(chunkSize);
        
        if (!chunk) {
            return false;
        }
        
        // 初始化内存块
        uint8_t* chunkPtr = chunk.get();
        for (size_t i = 0; i < m_config.initialBlocks; ++i) {
            Block* block = reinterpret_cast<Block*>(chunkPtr + i * (m_config.blockSize + sizeof(Block)));
            block->data = chunkPtr + i * (m_config.blockSize + sizeof(Block)) + sizeof(Block);
            block->size = m_config.blockSize;
            block->inUse = false;
            block->next = (i < m_config.initialBlocks - 1) ? 
                         reinterpret_cast<Block*>(chunkPtr + (i + 1) * (m_config.blockSize + sizeof(Block))) : 
                         m_freeList;
        }
        
        m_freeList = reinterpret_cast<Block*>(chunkPtr);
        m_totalBlocks += m_config.initialBlocks;
        m_chunks.push_back(std::move(chunk));
        
        return true;
    }
    catch (...) {
        return false;
    }
}

// ==================== TaskScheduler 实现 ====================

TaskScheduler& TaskScheduler::Instance() {
    static TaskScheduler instance;
    return instance;
}

TaskScheduler::~TaskScheduler() {
    Stop(true);
}

bool TaskScheduler::Start(uint32_t workerThreads) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_taskMutex);

        if (m_running.load()) {
            return true; // 已经在运行
        }

        // 确定工作线程数量
        if (workerThreads == 0) {
            workerThreads = std::max(1u, std::thread::hardware_concurrency());
        }

        m_running.store(true);

        // 启动工作线程
        m_workers.reserve(workerThreads);
        for (uint32_t i = 0; i < workerThreads; ++i) {
            m_workers.emplace_back(&TaskScheduler::WorkerThread, this);
        }

        UIEngine::WriteLog(0, L"Task scheduler started with " + std::to_wstring(workerThreads) + L" worker threads");
        return true;
    }
    catch (...) {
        UIEngine::WriteLog(2, L"Failed to start task scheduler");
        return false;
    }
}

bool TaskScheduler::Stop(bool waitForCompletion) noexcept {
    try {
        {
            std::lock_guard<std::mutex> lock(m_taskMutex);
            if (!m_running.load()) {
                return true; // 已经停止
            }

            m_running.store(false);
        }

        // 通知所有工作线程
        m_taskCondition.notify_all();

        // 等待工作线程结束
        for (auto& worker : m_workers) {
            if (worker.joinable()) {
                worker.join();
            }
        }

        m_workers.clear();

        if (!waitForCompletion) {
            std::lock_guard<std::mutex> lock(m_taskMutex);
            // 清空任务队列
            while (!m_taskQueue.empty()) {
                m_taskQueue.pop();
            }
            m_cancelledTasks.clear();
        }

        UIEngine::WriteLog(0, L"Task scheduler stopped");
        return true;
    }
    catch (...) {
        UIEngine::WriteLog(2, L"Failed to stop task scheduler");
        return false;
    }
}

uint64_t TaskScheduler::SubmitTask(std::function<void()> task,
                                  TaskPriority priority,
                                  const std::wstring& name) noexcept {
    try {
        if (!task || !m_running.load()) {
            return 0;
        }

        uint64_t taskId = m_nextTaskId.fetch_add(1);

        TaskInfo taskInfo;
        taskInfo.id = taskId;
        taskInfo.name = name.empty() ? (L"Task_" + std::to_wstring(taskId)) : name;
        taskInfo.priority = priority;
        taskInfo.task = std::move(task);
        taskInfo.scheduledTime = std::chrono::steady_clock::now();

        {
            std::lock_guard<std::mutex> lock(m_taskMutex);
            m_taskQueue.push(std::move(taskInfo));
        }

        m_taskCondition.notify_one();

        return taskId;
    }
    catch (...) {
        return 0;
    }
}

uint64_t TaskScheduler::SubmitDelayedTask(std::function<void()> task,
                                         uint32_t delayMs,
                                         TaskPriority priority,
                                         const std::wstring& name) noexcept {
    try {
        if (!task || !m_running.load()) {
            return 0;
        }

        uint64_t taskId = m_nextTaskId.fetch_add(1);

        TaskInfo taskInfo;
        taskInfo.id = taskId;
        taskInfo.name = name.empty() ? (L"DelayedTask_" + std::to_wstring(taskId)) : name;
        taskInfo.priority = priority;
        taskInfo.task = std::move(task);
        taskInfo.scheduledTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(delayMs);

        {
            std::lock_guard<std::mutex> lock(m_taskMutex);
            m_taskQueue.push(std::move(taskInfo));
        }

        m_taskCondition.notify_one();

        return taskId;
    }
    catch (...) {
        return 0;
    }
}

bool TaskScheduler::CancelTask(uint64_t taskId) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_taskMutex);
        m_cancelledTasks.insert(taskId);
        return true;
    }
    catch (...) {
        return false;
    }
}

bool TaskScheduler::GetStats(size_t& pendingTasks, size_t& completedTasks, size_t& workerThreads) noexcept {
    try {
        std::lock_guard<std::mutex> lock(m_taskMutex);

        pendingTasks = m_taskQueue.size();
        completedTasks = m_completedTasks.load();
        workerThreads = m_workers.size();

        return true;
    }
    catch (...) {
        return false;
    }
}

void TaskScheduler::WorkerThread() noexcept {
    try {
        while (m_running.load()) {
            TaskInfo taskInfo;
            bool hasTask = false;

            {
                std::unique_lock<std::mutex> lock(m_taskMutex);

                // 等待任务或停止信号
                m_taskCondition.wait(lock, [this] {
                    return !m_running.load() || !m_taskQueue.empty();
                });

                if (!m_running.load()) {
                    break;
                }

                if (!m_taskQueue.empty()) {
                    auto now = std::chrono::steady_clock::now();

                    // 检查队列顶部的任务是否到了执行时间
                    if (m_taskQueue.top().scheduledTime <= now) {
                        taskInfo = m_taskQueue.top();
                        m_taskQueue.pop();
                        hasTask = true;
                    }
                }
            }

            if (hasTask) {
                // 检查任务是否被取消
                {
                    std::lock_guard<std::mutex> lock(m_taskMutex);
                    if (m_cancelledTasks.find(taskInfo.id) != m_cancelledTasks.end()) {
                        m_cancelledTasks.erase(taskInfo.id);
                        continue;
                    }
                }

                // 执行任务
                try {
                    taskInfo.task();
                    m_completedTasks.fetch_add(1);
                }
                catch (...) {
                    UIEngine::WriteLog(2, L"Task execution failed: " + taskInfo.name);
                }
            } else {
                // 如果没有可执行的任务，短暂休眠
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
    }
    catch (...) {
        UIEngine::WriteLog(2, L"Worker thread exception");
    }
}

} // namespace HHBUI
